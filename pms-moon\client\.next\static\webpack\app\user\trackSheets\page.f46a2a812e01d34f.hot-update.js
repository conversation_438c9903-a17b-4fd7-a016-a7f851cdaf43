"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/createTrackSheet.tsx":
/*!***************************************************!*\
  !*** ./app/user/trackSheets/createTrackSheet.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/_component/FormInput */ \"(app-pages-browser)/./app/_component/FormInput.tsx\");\n/* harmony import */ var _app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/_component/FormCheckboxGroup */ \"(app-pages-browser)/./app/_component/FormCheckboxGroup.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* harmony import */ var _app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/_component/PageInput */ \"(app-pages-browser)/./app/_component/PageInput.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./LegrandDetailsComponent */ \"(app-pages-browser)/./app/user/trackSheets/LegrandDetailsComponent.tsx\");\n/* harmony import */ var _ClientSelectPage__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./ClientSelectPage */ \"(app-pages-browser)/./app/user/trackSheets/ClientSelectPage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst validateFtpPageFormat = (value)=>{\n    if (!value || value.trim() === \"\") return false;\n    const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n    const match = value.match(ftpPageRegex);\n    if (!match) return false;\n    const currentPage = parseInt(match[1], 10);\n    const totalPages = parseInt(match[2], 10);\n    return currentPage > 0 && totalPages > 0 && currentPage <= totalPages;\n};\nconst trackSheetSchema = zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Client is required\"),\n    entries: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n        company: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Company is required\"),\n        division: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoice: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice is required\"),\n        masterInvoice: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        bol: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoiceDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice date is required\"),\n        receivedDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Received date is required\"),\n        shipmentDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        carrierName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Carrier name is required\"),\n        invoiceStatus: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice status is required\"),\n        manualMatching: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Manual matching is required\"),\n        invoiceType: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice type is required\"),\n        billToClient: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        currency: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Currency is required\"),\n        qtyShipped: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        weightUnitName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Weight unit is required\"),\n        quantityBilledText: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        freightClass: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoiceTotal: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice total is required\"),\n        savings: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        financialNotes: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        ftpFileName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"FTP File Name is required\"),\n        ftpPage: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"FTP Page is required\").refine((value)=>validateFtpPageFormat(value), (value)=>{\n            if (!value || value.trim() === \"\") {\n                return {\n                    message: \"FTP Page is required\"\n                };\n            }\n            const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n            const match = value.match(ftpPageRegex);\n            if (!match) {\n                return {\n                    message: \"\"\n                };\n            }\n            const currentPage = parseInt(match[1], 10);\n            const totalPages = parseInt(match[2], 10);\n            if (currentPage <= 0 || totalPages <= 0) {\n                return {\n                    message: \"Page numbers must be positive (greater than 0)\"\n                };\n            }\n            if (currentPage > totalPages) {\n                return {\n                    message: \"Please enter a page number between \".concat(totalPages, \" and \").concat(currentPage, \" \")\n                };\n            }\n            return {\n                message: \"Invalid page format\"\n            };\n        }),\n        docAvailable: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.string()).optional().default([]),\n        otherDocuments: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        notes: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        //mistake: z.string().optional(),\n        legrandAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandCompanyName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        customFields: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_16__.z.string(),\n            name: zod__WEBPACK_IMPORTED_MODULE_16__.z.string(),\n            type: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n            value: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional()\n        })).default([])\n    }))\n});\nconst CreateTrackSheet = (param)=>{\n    let { client, carrier, associate, userData, activeView, setActiveView, permissions, carrierDataUpdate, clientDataUpdate } = param;\n    _s();\n    const companyFieldRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [generatedFilenames, setGeneratedFilenames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filenameValidation, setFilenameValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [missingFields, setMissingFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [legrandData, setLegrandData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [manualMatchingData, setManualMatchingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customFieldsRefresh, setCustomFieldsRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showFullForm, setShowFullForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [initialAssociateId, setInitialAssociateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialClientId, setInitialClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const selectionForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm)({\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\"\n        }\n    });\n    const associateOptions = associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n        var _a_id;\n        return {\n            value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n            label: a.name,\n            name: a.name\n        };\n    });\n    const carrierOptions = carrier === null || carrier === void 0 ? void 0 : carrier.map((c)=>{\n        var _c_id;\n        return {\n            value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n            label: c.name\n        };\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(trackSheetSchema),\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\",\n            entries: [\n                {\n                    company: \"\",\n                    division: \"\",\n                    invoice: \"\",\n                    masterInvoice: \"\",\n                    bol: \"\",\n                    invoiceDate: \"\",\n                    receivedDate: \"\",\n                    shipmentDate: \"\",\n                    carrierName: \"\",\n                    invoiceStatus: \"ENTRY\",\n                    manualMatching: \"\",\n                    invoiceType: \"\",\n                    billToClient: \"yes\",\n                    currency: \"\",\n                    qtyShipped: \"\",\n                    weightUnitName: \"\",\n                    quantityBilledText: \"\",\n                    freightClass: \"\",\n                    invoiceTotal: \"\",\n                    savings: \"\",\n                    financialNotes: \"\",\n                    ftpFileName: \"\",\n                    ftpPage: \"\",\n                    docAvailable: [],\n                    otherDocuments: \"\",\n                    notes: \"\",\n                    //mistake: \"\",\n                    legrandAlias: \"\",\n                    legrandCompanyName: \"\",\n                    legrandAddress: \"\",\n                    legrandZipcode: \"\",\n                    shipperAlias: \"\",\n                    shipperAddress: \"\",\n                    shipperZipcode: \"\",\n                    consigneeAlias: \"\",\n                    consigneeAddress: \"\",\n                    consigneeZipcode: \"\",\n                    billtoAlias: \"\",\n                    billtoAddress: \"\",\n                    billtoZipcode: \"\",\n                    customFields: []\n                }\n            ]\n        }\n    });\n    const getFilteredClientOptions = ()=>{\n        if (!initialAssociateId) {\n            return (client === null || client === void 0 ? void 0 : client.map((c)=>{\n                var _c_id;\n                return {\n                    value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                    label: c.client_name,\n                    name: c.client_name\n                };\n            })) || [];\n        }\n        const filteredClients = (client === null || client === void 0 ? void 0 : client.filter((c)=>{\n            var _c_associateId;\n            return ((_c_associateId = c.associateId) === null || _c_associateId === void 0 ? void 0 : _c_associateId.toString()) === initialAssociateId;\n        })) || [];\n        return filteredClients.map((c)=>{\n            var _c_id;\n            return {\n                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                label: c.client_name,\n                name: c.client_name\n            };\n        });\n    };\n    const clientOptions = getFilteredClientOptions();\n    const entries = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useWatch)({\n        control: form.control,\n        name: \"entries\"\n    });\n    const validateClientForAssociate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, currentClientId)=>{\n        if (associateId && currentClientId) {\n            var _currentClient_associateId;\n            const currentClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === currentClientId;\n            });\n            if (currentClient && ((_currentClient_associateId = currentClient.associateId) === null || _currentClient_associateId === void 0 ? void 0 : _currentClient_associateId.toString()) !== associateId) {\n                form.setValue(\"clientId\", \"\");\n                setInitialClientId(\"\");\n                return false;\n            }\n        }\n        return true;\n    }, [\n        client,\n        form\n    ]);\n    const clearEntrySpecificClients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const currentEntries = form.getValues(\"entries\") || [];\n        if (currentEntries.length > 0) {\n            const hasEntrySpecificClients = currentEntries.some((entry)=>entry.clientId);\n            if (hasEntrySpecificClients) {\n                const updatedEntries = currentEntries.map((entry)=>({\n                        ...entry,\n                        clientId: \"\"\n                    }));\n                form.setValue(\"entries\", updatedEntries);\n            }\n        }\n    }, [\n        form\n    ]);\n    const fetchLegrandData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.legrandMapping_routes.GET_LEGRAND_MAPPINGS);\n            if (response && Array.isArray(response)) {\n                setLegrandData(response);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Error fetching LEGRAND mapping data:\", error);\n        }\n    }, []);\n    const fetchManualMatchingData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.manualMatchingMapping_routes.GET_MANUAL_MATCHING_MAPPINGS);\n            if (response && Array.isArray(response)) {\n                setManualMatchingData(response);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Error fetching manual matching mapping data:\", error);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchLegrandData();\n        fetchManualMatchingData();\n    }, [\n        fetchLegrandData,\n        fetchManualMatchingData\n    ]);\n    const handleLegrandDataChange = (entryIndex, businessUnit, divisionCode)=>{\n        form.setValue(\"entries.\".concat(entryIndex, \".company\"), businessUnit);\n        if (divisionCode) {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), divisionCode);\n            handleManualMatchingAutoFill(entryIndex, divisionCode);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), \"\");\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), \"\");\n        }\n    };\n    const handleManualMatchingAutoFill = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, division)=>{\n        var _formValues_entries, _clientOptions_find;\n        if (!division || !manualMatchingData.length) {\n            return;\n        }\n        const formValues = form.getValues();\n        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        if (entryClientName !== \"LEGRAND\") {\n            return;\n        }\n        const matchingEntry = manualMatchingData.find((mapping)=>mapping.division === division);\n        if (matchingEntry && matchingEntry.ManualShipment) {\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), matchingEntry.ManualShipment);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), \"\");\n        }\n    }, [\n        form,\n        manualMatchingData,\n        clientOptions\n    ]);\n    const fetchCustomFieldsForClient = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (clientId)=>{\n        if (!clientId) return [];\n        try {\n            const allCustomFieldsResponse = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS, \"/\").concat(clientId));\n            let customFieldsData = [];\n            if (allCustomFieldsResponse && allCustomFieldsResponse.custom_fields && allCustomFieldsResponse.custom_fields.length > 0) {\n                customFieldsData = allCustomFieldsResponse.custom_fields.map((field)=>{\n                    let autoFilledValue = \"\";\n                    if (field.type === \"AUTO\") {\n                        if (field.autoOption === \"DATE\") {\n                            autoFilledValue = new Date().toISOString().split(\"T\")[0];\n                        } else if (field.autoOption === \"USERNAME\") {\n                            autoFilledValue = (userData === null || userData === void 0 ? void 0 : userData.username) || \"\";\n                        }\n                    }\n                    return {\n                        id: field.id,\n                        name: field.name,\n                        type: field.type,\n                        autoOption: field.autoOption,\n                        value: autoFilledValue\n                    };\n                });\n            }\n            return customFieldsData;\n        } catch (error) {\n            return [];\n        }\n    }, [\n        userData\n    ]);\n    // const { fields, append, remove } = useFieldArray({\n    //   control: form.control,\n    //   name: \"entries\",\n    // });\n    // For now, we'll work with a single entry\n    const fields = [\n        {\n            id: \"single-entry\"\n        }\n    ];\n    // const append = () => {};\n    // const remove = () => {};\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        companyFieldRefs.current = companyFieldRefs.current.slice(0, fields.length);\n    }, [\n        fields.length\n    ]);\n    const generateFilename = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, formValues)=>{\n        try {\n            const entry = formValues.entries[entryIndex];\n            if (!entry) return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Entry data\"\n                ]\n            };\n            const missing = [];\n            const selectedAssociate = associate === null || associate === void 0 ? void 0 : associate.find((a)=>{\n                var _a_id;\n                return ((_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString()) === formValues.associateId;\n            });\n            const associateName = (selectedAssociate === null || selectedAssociate === void 0 ? void 0 : selectedAssociate.name) || \"\";\n            if (!associateName) {\n                missing.push(\"Associate\");\n            }\n            const entryClientId = entry.clientId || formValues.clientId;\n            const selectedClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entryClientId;\n            });\n            const clientName = (selectedClient === null || selectedClient === void 0 ? void 0 : selectedClient.client_name) || \"\";\n            if (!clientName) {\n                missing.push(\"Client\");\n            }\n            let carrierName = \"\";\n            if (entry.carrierName) {\n                const carrierOption = carrier === null || carrier === void 0 ? void 0 : carrier.find((c)=>{\n                    var _c_id;\n                    return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entry.carrierName;\n                });\n                carrierName = (carrierOption === null || carrierOption === void 0 ? void 0 : carrierOption.name) || \"\";\n            }\n            if (!carrierName) {\n                missing.push(\"Carrier\");\n            }\n            const receivedDate = entry.receivedDate;\n            const invoiceDate = entry.invoiceDate;\n            const currentDate = new Date();\n            const year = currentDate.getFullYear().toString();\n            const month = currentDate.toLocaleString(\"default\", {\n                month: \"short\"\n            }).toUpperCase();\n            if (!invoiceDate) {\n                missing.push(\"Invoice Date\");\n            }\n            let receivedDateStr = \"\";\n            if (receivedDate) {\n                const dateParts = receivedDate.split(\"/\");\n                if (dateParts.length === 3) {\n                    const day = parseInt(dateParts[0], 10);\n                    const month = parseInt(dateParts[1], 10);\n                    const year = parseInt(dateParts[2], 10);\n                    const paddedMonth = month.toString().padStart(2, \"0\");\n                    const paddedDay = day.toString().padStart(2, \"0\");\n                    receivedDateStr = \"\".concat(year, \"-\").concat(paddedMonth, \"-\").concat(paddedDay);\n                } else {\n                    const date = new Date(receivedDate);\n                    receivedDateStr = date.toISOString().split(\"T\")[0];\n                }\n            } else {\n                missing.push(\"Received Date\");\n            }\n            const ftpFileName = entry.ftpFileName || \"\";\n            const baseFilename = ftpFileName ? ftpFileName.endsWith(\".pdf\") ? ftpFileName : \"\".concat(ftpFileName, \".pdf\") : \"\";\n            if (!baseFilename) {\n                missing.push(\"FTP File Name\");\n            }\n            const isValid = missing.length === 0;\n            const filename = isValid ? \"/\".concat(associateName, \"/\").concat(clientName, \"/CARRIERINVOICES/\").concat(carrierName, \"/\").concat(year, \"/\").concat(month, \"/\").concat(receivedDateStr, \"/\").concat(baseFilename) : \"\";\n            return {\n                filename,\n                isValid,\n                missing\n            };\n        } catch (error) {\n            return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Error generating filename\"\n                ]\n            };\n        }\n    }, [\n        client,\n        carrier,\n        associate\n    ]);\n    const handleCompanyAutoPopulation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, entryClientId)=>{\n        var _clientOptions_find;\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        const currentEntry = form.getValues(\"entries.\".concat(entryIndex));\n        if (entryClientName && entryClientName !== \"LEGRAND\") {\n            form.setValue(\"entries.\".concat(entryIndex, \".company\"), entryClientName);\n        } else if (entryClientName === \"LEGRAND\") {\n            const shipperAlias = currentEntry.shipperAlias;\n            const consigneeAlias = currentEntry.consigneeAlias;\n            const billtoAlias = currentEntry.billtoAlias;\n            const hasAnyLegrandData = shipperAlias || consigneeAlias || billtoAlias;\n            if (!hasAnyLegrandData && currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        } else {\n            if (currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        }\n    }, [\n        form,\n        clientOptions\n    ]);\n    const handleCustomFieldsFetch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (entryIndex, entryClientId)=>{\n        var _currentCustomFields_, _currentCustomFields_1;\n        if (!entryClientId) {\n            const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\"));\n            if (currentCustomFields && currentCustomFields.length > 0) {\n                form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), []);\n            }\n            return;\n        }\n        const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\")) || [];\n        const hasEmptyAutoUsernameFields = currentCustomFields.some((field)=>field.type === \"AUTO\" && field.autoOption === \"USERNAME\" && !field.value && (userData === null || userData === void 0 ? void 0 : userData.username));\n        const shouldFetchCustomFields = currentCustomFields.length === 0 || currentCustomFields.length > 0 && !((_currentCustomFields_ = currentCustomFields[0]) === null || _currentCustomFields_ === void 0 ? void 0 : _currentCustomFields_.clientId) || ((_currentCustomFields_1 = currentCustomFields[0]) === null || _currentCustomFields_1 === void 0 ? void 0 : _currentCustomFields_1.clientId) !== entryClientId || hasEmptyAutoUsernameFields;\n        if (shouldFetchCustomFields) {\n            const customFieldsData = await fetchCustomFieldsForClient(entryClientId);\n            const fieldsWithClientId = customFieldsData.map((field)=>({\n                    ...field,\n                    clientId: entryClientId\n                }));\n            form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), fieldsWithClientId);\n            setTimeout(()=>{\n                fieldsWithClientId.forEach((field, fieldIndex)=>{\n                    const fieldPath = \"entries.\".concat(entryIndex, \".customFields.\").concat(fieldIndex, \".value\");\n                    if (field.value) {\n                        form.setValue(fieldPath, field.value);\n                    }\n                });\n                setCustomFieldsRefresh((prev)=>prev + 1);\n            }, 100);\n        }\n    }, [\n        form,\n        fetchCustomFieldsForClient,\n        userData === null || userData === void 0 ? void 0 : userData.username\n    ]);\n    const updateFilenames = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const formValues = form.getValues();\n        const newFilenames = [];\n        const newValidation = [];\n        const newMissingFields = [];\n        if (formValues.entries && Array.isArray(formValues.entries)) {\n            formValues.entries.forEach((_, index)=>{\n                const { filename, isValid, missing } = generateFilename(index, formValues);\n                newFilenames[index] = filename;\n                newValidation[index] = isValid;\n                newMissingFields[index] = missing || [];\n            });\n        }\n        setGeneratedFilenames(newFilenames);\n        setFilenameValidation(newValidation);\n        setMissingFields(newMissingFields);\n    }, [\n        form,\n        generateFilename\n    ]);\n    const handleInitialSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, clientId)=>{\n        form.setValue(\"associateId\", associateId);\n        form.setValue(\"clientId\", clientId);\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(0, clientId);\n            handleCustomFieldsFetch(0, clientId);\n            updateFilenames();\n        }, 50);\n        setShowFullForm(true);\n    }, [\n        form,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch,\n        updateFilenames\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timeoutId = setTimeout(()=>{\n            updateFilenames();\n            const formValues = form.getValues();\n            if (formValues.entries && Array.isArray(formValues.entries)) {\n                formValues.entries.forEach((entry, index)=>{\n                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (index === 0 ? formValues.clientId : \"\");\n                    if (entryClientId) {\n                        handleCustomFieldsFetch(index, entryClientId);\n                    }\n                });\n            }\n        }, 50);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        updateFilenames,\n        handleCustomFieldsFetch,\n        form\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const subscription = form.watch((_, param)=>{\n            let { name } = param;\n            if (name && (name.includes(\"associateId\") || name.includes(\"clientId\") || name.includes(\"carrierName\") || name.includes(\"invoiceDate\") || name.includes(\"receivedDate\") || name.includes(\"ftpFileName\") || name.includes(\"company\") || name.includes(\"division\"))) {\n                updateFilenames();\n                if (name.includes(\"division\")) {\n                    const entryMatch = name.match(/entries\\.(\\d+)\\.division/);\n                    if (entryMatch) {\n                        var _formValues_entries, _clientOptions_find;\n                        const entryIndex = parseInt(entryMatch[1], 10);\n                        const formValues = form.getValues();\n                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n                        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n                        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                        if (entryClientName === \"LEGRAND\") {\n                            var _formValues_entries_entryIndex, _formValues_entries1;\n                            const divisionValue = (_formValues_entries1 = formValues.entries) === null || _formValues_entries1 === void 0 ? void 0 : (_formValues_entries_entryIndex = _formValues_entries1[entryIndex]) === null || _formValues_entries_entryIndex === void 0 ? void 0 : _formValues_entries_entryIndex.division;\n                            if (divisionValue) {\n                                setTimeout(()=>{\n                                    handleManualMatchingAutoFill(entryIndex, divisionValue);\n                                }, 50);\n                            }\n                        }\n                    }\n                }\n                if (name.includes(\"clientId\")) {\n                    const entryMatch = name.match(/entries\\.(\\d+)\\.clientId/);\n                    if (entryMatch) {\n                        const entryIndex = parseInt(entryMatch[1], 10);\n                        setTimeout(()=>{\n                            var _formValues_entries, _clientOptions_find;\n                            const formValues = form.getValues();\n                            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n                            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                            if (entryClientName === \"LEGRAND\" && (entry === null || entry === void 0 ? void 0 : entry.division)) {\n                                handleManualMatchingAutoFill(entryIndex, entry.division);\n                            }\n                        }, 100);\n                    }\n                }\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        form,\n        updateFilenames,\n        handleManualMatchingAutoFill,\n        clientOptions\n    ]);\n    const onSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (values)=>{\n        try {\n            const currentFormValues = form.getValues();\n            const currentValidation = [];\n            const currentMissingFields = [];\n            const currentFilenames = [];\n            if (currentFormValues.entries && Array.isArray(currentFormValues.entries)) {\n                currentFormValues.entries.forEach((_, index)=>{\n                    const { filename, isValid, missing } = generateFilename(index, currentFormValues);\n                    currentValidation[index] = isValid;\n                    currentMissingFields[index] = missing || [];\n                    currentFilenames[index] = filename;\n                });\n            }\n            const allFilenamesValid = currentValidation.every((isValid)=>isValid);\n            if (!allFilenamesValid) {\n                const invalidEntries = currentValidation.map((isValid, index)=>({\n                        index,\n                        isValid,\n                        missing: currentMissingFields[index]\n                    })).filter((entry)=>!entry.isValid);\n                const errorDetails = invalidEntries.map((entry)=>\"Entry \".concat(entry.index + 1, \": \").concat(entry.missing.join(\", \"))).join(\" | \");\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Cannot submit: Missing fields - \".concat(errorDetails));\n                return;\n            }\n            const entries = values.entries.map((entry, index)=>{\n                var _entry_customFields;\n                return {\n                    company: entry.company,\n                    division: entry.division,\n                    invoice: entry.invoice,\n                    masterInvoice: entry.masterInvoice,\n                    bol: entry.bol,\n                    invoiceDate: entry.invoiceDate,\n                    receivedDate: entry.receivedDate,\n                    shipmentDate: entry.shipmentDate,\n                    carrierId: entry.carrierName,\n                    invoiceStatus: entry.invoiceStatus,\n                    manualMatching: entry.manualMatching,\n                    invoiceType: entry.invoiceType,\n                    billToClient: entry.billToClient,\n                    currency: entry.currency,\n                    qtyShipped: entry.qtyShipped,\n                    weightUnitName: entry.weightUnitName,\n                    quantityBilledText: entry.quantityBilledText,\n                    freightClass: entry.freightClass,\n                    invoiceTotal: entry.invoiceTotal,\n                    savings: entry.savings,\n                    ftpFileName: entry.ftpFileName,\n                    ftpPage: entry.ftpPage,\n                    docAvailable: entry.docAvailable,\n                    notes: entry.notes,\n                    //mistake: entry.mistake,\n                    filePath: generatedFilenames[index],\n                    customFields: (_entry_customFields = entry.customFields) === null || _entry_customFields === void 0 ? void 0 : _entry_customFields.map((cf)=>({\n                            id: cf.id,\n                            value: cf.value\n                        }))\n                };\n            });\n            const formData = {\n                clientId: values.clientId,\n                entries: entries\n            };\n            const result = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.formSubmit)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.trackSheets_routes.CREATE_TRACK_SHEETS, \"POST\", formData);\n            if (result.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"All TrackSheets created successfully\");\n                form.reset();\n                setTimeout(()=>{\n                    handleInitialSelection(initialAssociateId, initialClientId);\n                }, 100);\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(result.message || \"Failed to create TrackSheets\");\n            }\n            router.refresh();\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"An error occurred while creating the TrackSheets\");\n        }\n    }, [\n        form,\n        router,\n        generateFilename,\n        initialAssociateId,\n        initialClientId,\n        handleInitialSelection,\n        generatedFilenames\n    ]);\n    // const addNewEntry = useCallback(() => {\n    //   const newIndex = fields.length;\n    //   append({\n    //     clientId: initialClientId,\n    //     company: \"\",\n    //     division: \"\",\n    //     invoice: \"\",\n    //     masterInvoice: \"\",\n    //     bol: \"\",\n    //     invoiceDate: \"\",\n    //     receivedDate: \"\",\n    //     shipmentDate: \"\",\n    //     carrierName: \"\",\n    //     invoiceStatus: \"ENTRY\",\n    //     manualMatching: \"\",\n    //     invoiceType: \"\",\n    //     billToClient: \"\",\n    //     currency: \"\",\n    //     qtyShipped: \"\",\n    //     weightUnitName: \"\",\n    //     quantityBilledText: \"\",\n    //     invoiceTotal: \"\",\n    //     savings: \"\",\n    //     financialNotes: \"\",\n    //     ftpFileName: \"\",\n    //     ftpPage: \"\",\n    //     docAvailable: [],\n    //     otherDocuments: \"\",\n    //     notes: \"\",\n    //     //mistake: \"\",\n    //     legrandAlias: \"\",\n    //     legrandCompanyName: \"\",\n    //     legrandAddress: \"\",\n    //     legrandZipcode: \"\",\n    //     shipperAlias: \"\",\n    //     shipperAddress: \"\",\n    //     shipperZipcode: \"\",\n    //     consigneeAlias: \"\",\n    //     consigneeAddress: \"\",\n    //     consigneeZipcode: \"\",\n    //     billtoAlias: \"\",\n    //     billtoAddress: \"\",\n    //     billtoZipcode: \"\",\n    //     customFields: [],\n    //   } as any);\n    //   setTimeout(() => {\n    //     handleCompanyAutoPopulation(newIndex, initialClientId);\n    //     handleCustomFieldsFetch(newIndex, initialClientId);\n    //     if (companyFieldRefs.current[newIndex]) {\n    //       const inputElement =\n    //         companyFieldRefs.current[newIndex]?.querySelector(\"input\") ||\n    //         companyFieldRefs.current[newIndex]?.querySelector(\"button\") ||\n    //         companyFieldRefs.current[newIndex]?.querySelector(\"select\");\n    //       if (inputElement) {\n    //         inputElement.focus();\n    //         try {\n    //           inputElement.click();\n    //         } catch (e) {}\n    //       }\n    //     }\n    //     updateFilenames();\n    //   }, 200);\n    // }, [\n    //   append,\n    //   fields.length,\n    //   updateFilenames,\n    //   initialClientId,\n    //   handleCompanyAutoPopulation,\n    //   handleCustomFieldsFetch,\n    // ]);\n    const handleFormKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.ctrlKey && (e.key === \"s\" || e.key === \"S\")) {\n            e.preventDefault();\n            form.handleSubmit(onSubmit)();\n        } else if (e.key === \"Enter\" && !e.ctrlKey && !e.shiftKey && !e.altKey) {\n            const activeElement = document.activeElement;\n            const isSubmitButton = (activeElement === null || activeElement === void 0 ? void 0 : activeElement.getAttribute(\"type\")) === \"submit\";\n            if (isSubmitButton) {\n                e.preventDefault();\n                form.handleSubmit(onSubmit)();\n            }\n        }\n    }, [\n        form,\n        onSubmit\n    ]);\n    // const removeEntry = (index: number) => {\n    //   if (fields.length > 1) {\n    //     remove(index);\n    //   } else {\n    //     toast.error(\"You must have at least one entry\");\n    //   }\n    // };\n    const getFilteredDivisionOptions = (company, entryIndex)=>{\n        if (!company || !legrandData.length) {\n            return [];\n        }\n        if (entryIndex !== undefined) {\n            var _formValues_entries, _clientOptions_find;\n            const formValues = form.getValues();\n            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n            if (entryClientName === \"LEGRAND\") {\n                const shipperAlias = form.getValues(\"entries.\".concat(entryIndex, \".shipperAlias\"));\n                const consigneeAlias = form.getValues(\"entries.\".concat(entryIndex, \".consigneeAlias\"));\n                const billtoAlias = form.getValues(\"entries.\".concat(entryIndex, \".billtoAlias\"));\n                const currentAlias = shipperAlias || consigneeAlias || billtoAlias;\n                if (currentAlias) {\n                    const selectedData = legrandData.find((data)=>{\n                        const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                        return uniqueKey === currentAlias;\n                    });\n                    if (selectedData) {\n                        const baseAliasName = selectedData.aliasShippingNames && selectedData.aliasShippingNames !== \"NONE\" ? selectedData.aliasShippingNames : selectedData.legalName;\n                        const sameAliasEntries = legrandData.filter((data)=>{\n                            const dataAliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : data.legalName;\n                            return dataAliasName === baseAliasName;\n                        });\n                        const allDivisions = [];\n                        sameAliasEntries.forEach((entry)=>{\n                            if (entry.customeCode) {\n                                if (entry.customeCode.includes(\"/\")) {\n                                    const splitDivisions = entry.customeCode.split(\"/\").map((d)=>d.trim());\n                                    allDivisions.push(...splitDivisions);\n                                } else {\n                                    allDivisions.push(entry.customeCode);\n                                }\n                            }\n                        });\n                        const uniqueDivisions = Array.from(new Set(allDivisions.filter((code)=>code)));\n                        if (uniqueDivisions.length > 1) {\n                            const contextDivisions = uniqueDivisions.sort().map((code)=>({\n                                    value: code,\n                                    label: code\n                                }));\n                            return contextDivisions;\n                        } else {}\n                    }\n                }\n            }\n        }\n        const allDivisions = [];\n        legrandData.filter((data)=>data.businessUnit === company && data.customeCode).forEach((data)=>{\n            if (data.customeCode.includes(\"/\")) {\n                const splitDivisions = data.customeCode.split(\"/\").map((d)=>d.trim());\n                allDivisions.push(...splitDivisions);\n            } else {\n                allDivisions.push(data.customeCode);\n            }\n        });\n        const divisions = Array.from(new Set(allDivisions.filter((code)=>code))).sort().map((code)=>({\n                value: code,\n                label: code\n            }));\n        return divisions;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-2 py-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4 pl-3 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                variant: \"default\",\n                                onClick: ()=>setActiveView(\"view\"),\n                                className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"view\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                children: \"View TrackSheet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1130,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                variant: \"default\",\n                                onClick: ()=>setActiveView(\"create\"),\n                                className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"create\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                children: \"Create TrackSheet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1141,\n                                columnNumber: 13\n                            }, undefined),\n                            activeView === \"create\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-2 ml-4 flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-4 h-4 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1157,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-sm font-semibold text-gray-900\",\n                                                children: \"Create TrackSheet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1158,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1156,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n                                        ...selectionForm,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        form: selectionForm,\n                                                        name: \"associateId\",\n                                                        label: \"Select Associate\",\n                                                        placeholder: \"Search Associate...\",\n                                                        isRequired: true,\n                                                        options: associateOptions || [],\n                                                        onValueChange: (value)=>{\n                                                            setInitialAssociateId(value);\n                                                            if (value && initialClientId) {\n                                                                validateClientForAssociate(value, initialClientId);\n                                                            } else {\n                                                                setInitialClientId(\"\");\n                                                                selectionForm.setValue(\"clientId\", \"\");\n                                                            }\n                                                            setShowFullForm(false);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1166,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1165,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        form: selectionForm,\n                                                        name: \"clientId\",\n                                                        label: \"Select Client\",\n                                                        placeholder: \"Search Client...\",\n                                                        isRequired: true,\n                                                        disabled: !initialAssociateId,\n                                                        options: clientOptions || [],\n                                                        onValueChange: (value)=>{\n                                                            setInitialClientId(value);\n                                                            if (showFullForm) {\n                                                                form.reset();\n                                                                clearEntrySpecificClients();\n                                                            }\n                                                            if (value && initialAssociateId) {\n                                                                setTimeout(()=>{\n                                                                    handleInitialSelection(initialAssociateId, value);\n                                                                }, 100);\n                                                            } else {\n                                                                setShowFullForm(false);\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1188,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1187,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                            lineNumber: 1164,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1163,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1155,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1129,\n                        columnNumber: 11\n                    }, undefined),\n                    activeView === \"view\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientSelectPage__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            permissions: permissions,\n                            client: client,\n                            clientDataUpdate: clientDataUpdate,\n                            carrierDataUpdate: carrierDataUpdate\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                            lineNumber: 1223,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1222,\n                        columnNumber: 13\n                    }, undefined) : /* Form Section - Only show when both associate and client are selected */ showFullForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n                        ...form,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: form.handleSubmit(onSubmit),\n                            onKeyDown: handleFormKeyDown,\n                            className: \"space-y-3\",\n                            children: [\n                                fields.map((field, index)=>{\n                                    var _missingFields_index;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2 bg-gray-100 rounded-md px-3 py-2 border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold text-xs\",\n                                                            children: index + 1\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1244,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                            children: [\n                                                                \"Entry #\",\n                                                                index + 1\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1247,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1243,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1242,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 rounded-md p-2 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1260,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: \"Client Information\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1261,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1259,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"mt-2\",\n                                                                                form: form,\n                                                                                label: \"FTP File Name\",\n                                                                                name: \"entries.\".concat(index, \".ftpFileName\"),\n                                                                                type: \"text\",\n                                                                                isRequired: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1268,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1267,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"mt-2\",\n                                                                            form: form,\n                                                                            label: \"FTP Page\",\n                                                                            name: \"entries.\".concat(index, \".ftpPage\"),\n                                                                            isRequired: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1277,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"mt-0\",\n                                                                                form: form,\n                                                                                name: \"entries.\".concat(index, \".carrierName\"),\n                                                                                label: \"Select Carrier\",\n                                                                                placeholder: \"Search Carrier\",\n                                                                                isRequired: true,\n                                                                                options: (carrierOptions === null || carrierOptions === void 0 ? void 0 : carrierOptions.filter((carrier)=>{\n                                                                                    const currentEntries = form.getValues(\"entries\") || [];\n                                                                                    const isSelectedInOtherEntries = currentEntries.some((entry, entryIndex)=>entryIndex !== index && entry.carrierName === carrier.value);\n                                                                                    return !isSelectedInOtherEntries;\n                                                                                })) || [],\n                                                                                onValueChange: ()=>{\n                                                                                    setTimeout(()=>updateFilenames(), 100);\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1285,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1284,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: (()=>{\n                                                                                var _formValues_entries, _clientOptions_find;\n                                                                                const formValues = form.getValues();\n                                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-sm font-medium text-gray-700 mb-1 block\",\n                                                                                            children: [\n                                                                                                \"Billed to \",\n                                                                                                entryClientName || \"Client\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1327,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-4\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                    className: \"flex items-center\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                            type: \"radio\",\n                                                                                                            ...form.register(\"entries.\".concat(index, \".billToClient\")),\n                                                                                                            value: \"yes\",\n                                                                                                            defaultChecked: true,\n                                                                                                            className: \"mr-2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1332,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            className: \"text-sm\",\n                                                                                                            children: \"Yes\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1341,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1331,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                    className: \"flex items-center\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                            type: \"radio\",\n                                                                                                            ...form.register(\"entries.\".concat(index, \".billToClient\")),\n                                                                                                            value: \"no\",\n                                                                                                            className: \"mr-2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1344,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            className: \"text-sm\",\n                                                                                                            children: \"No\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1352,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1343,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1330,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1326,\n                                                                                    columnNumber: 37\n                                                                                }, undefined);\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1310,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1266,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                (()=>{\n                                                                    var _formValues_entries, _clientOptions_find;\n                                                                    const formValues = form.getValues();\n                                                                    const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                    const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                    return entryClientName === \"LEGRAND\";\n                                                                })() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-3 mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Shipper\",\n                                                                                fieldPrefix: \"shipper\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1375,\n                                                                                columnNumber: 35\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Consignee\",\n                                                                                fieldPrefix: \"consignee\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1384,\n                                                                                columnNumber: 35\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Bill-to\",\n                                                                                fieldPrefix: \"billto\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1393,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1374,\n                                                                        columnNumber: 33\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1373,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            ref: (el)=>{\n                                                                                companyFieldRefs.current[index] = el;\n                                                                            },\n                                                                            className: \"flex flex-col mb-1 [&_input]:h-10\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                form: form,\n                                                                                label: \"Company\",\n                                                                                name: \"entries.\".concat(index, \".company\"),\n                                                                                type: \"text\",\n                                                                                disable: (()=>{\n                                                                                    var _formValues_entries, _clientOptions_find;\n                                                                                    const formValues = form.getValues();\n                                                                                    const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                    const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                    return entryClientName === \"LEGRAND\";\n                                                                                })()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1414,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1408,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: (()=>{\n                                                                                var _formValues_entries, _clientOptions_find, _entries_index;\n                                                                                const formValues = form.getValues();\n                                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                const isLegrand = entryClientName === \"LEGRAND\";\n                                                                                return isLegrand ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    form: form,\n                                                                                    name: \"entries.\".concat(index, \".division\"),\n                                                                                    label: \"Division\",\n                                                                                    placeholder: \"Search Division\",\n                                                                                    disabled: false,\n                                                                                    options: getFilteredDivisionOptions((entries === null || entries === void 0 ? void 0 : (_entries_index = entries[index]) === null || _entries_index === void 0 ? void 0 : _entries_index.company) || \"\", index),\n                                                                                    onValueChange: (value)=>{\n                                                                                        setTimeout(()=>{\n                                                                                            handleManualMatchingAutoFill(index, value);\n                                                                                        }, 10);\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1454,\n                                                                                    columnNumber: 37\n                                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                    form: form,\n                                                                                    label: \"Division\",\n                                                                                    name: \"entries.\".concat(index, \".division\"),\n                                                                                    type: \"text\",\n                                                                                    placeholder: \"Enter Division\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1474,\n                                                                                    columnNumber: 37\n                                                                                }, undefined);\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1436,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: (()=>{\n                                                                                var _formValues_entries, _clientOptions_find;\n                                                                                const formValues = form.getValues();\n                                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                const isLegrand = entryClientName === \"LEGRAND\";\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                    form: form,\n                                                                                    label: isLegrand ? \"Manual or Matching\" : \"Manual or Matching\",\n                                                                                    name: \"entries.\".concat(index, \".manualMatching\"),\n                                                                                    type: \"text\",\n                                                                                    isRequired: true,\n                                                                                    disable: isLegrand,\n                                                                                    placeholder: isLegrand ? \"Auto-filled based on division\" : \"Enter manual or matching\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1502,\n                                                                                    columnNumber: 37\n                                                                                }, undefined);\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1484,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1407,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1258,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1256,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1529,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Document Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1530,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1528,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Master Invoice\",\n                                                                        name: \"entries.\".concat(index, \".masterInvoice\"),\n                                                                        type: \"text\",\n                                                                        onBlur: (e)=>{\n                                                                            const masterInvoiceValue = e.target.value;\n                                                                            if (masterInvoiceValue) {\n                                                                                form.setValue(\"entries.\".concat(index, \".invoice\"), masterInvoiceValue);\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1535,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice\",\n                                                                        name: \"entries.\".concat(index, \".invoice\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1550,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"BOL\",\n                                                                        name: \"entries.\".concat(index, \".bol\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1557,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1534,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Received Date\",\n                                                                        name: \"entries.\".concat(index, \".receivedDate\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true,\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1565,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Date\",\n                                                                        name: \"entries.\".concat(index, \".invoiceDate\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true,\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1573,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Shipment Date\",\n                                                                        name: \"entries.\".concat(index, \".shipmentDate\"),\n                                                                        type: \"text\",\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1581,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1564,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1527,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4 pb-4 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1594,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Financial & Shipment\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1595,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1593,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Total\",\n                                                                        name: \"entries.\".concat(index, \".invoiceTotal\"),\n                                                                        type: \"number\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1600,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        form: form,\n                                                                        name: \"entries.\".concat(index, \".currency\"),\n                                                                        label: \"Currency\",\n                                                                        placeholder: \"Search currency\",\n                                                                        isRequired: true,\n                                                                        options: [\n                                                                            {\n                                                                                value: \"USD\",\n                                                                                label: \"USD\"\n                                                                            },\n                                                                            {\n                                                                                value: \"CAD\",\n                                                                                label: \"CAD\"\n                                                                            },\n                                                                            {\n                                                                                value: \"EUR\",\n                                                                                label: \"EUR\"\n                                                                            }\n                                                                        ]\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1607,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Savings\",\n                                                                        name: \"entries.\".concat(index, \".savings\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1619,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Notes\",\n                                                                        name: \"entries.\".concat(index, \".financialNotes\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1625,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1599,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Freight Class\",\n                                                                        name: \"entries.\".concat(index, \".freightClass\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1633,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Weight Unit\",\n                                                                        name: \"entries.\".concat(index, \".weightUnitName\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1639,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Quantity Billed Text\",\n                                                                        name: \"entries.\".concat(index, \".quantityBilledText\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1646,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Quantity Shipped\",\n                                                                        name: \"entries.\".concat(index, \".qtyShipped\"),\n                                                                        type: \"number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1652,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1632,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        form: form,\n                                                                        name: \"entries.\".concat(index, \".invoiceType\"),\n                                                                        label: \"Invoice Type\",\n                                                                        placeholder: \"Search Invoice Type\",\n                                                                        isRequired: true,\n                                                                        options: [\n                                                                            {\n                                                                                value: \"FREIGHT\",\n                                                                                label: \"FREIGHT\"\n                                                                            },\n                                                                            {\n                                                                                value: \"ADDITIONAL\",\n                                                                                label: \"ADDITIONAL\"\n                                                                            },\n                                                                            {\n                                                                                value: \"BALANCED DUE\",\n                                                                                label: \"BALANCED DUE\"\n                                                                            },\n                                                                            {\n                                                                                value: \"CREDIT\",\n                                                                                label: \"CREDIT\"\n                                                                            },\n                                                                            {\n                                                                                value: \"REVISED\",\n                                                                                label: \"REVISED\"\n                                                                            }\n                                                                        ]\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1660,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Status\",\n                                                                        name: \"entries.\".concat(index, \".invoiceStatus\"),\n                                                                        type: \"text\",\n                                                                        disable: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1677,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1684,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1685,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1659,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1592,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1692,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Additional Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1693,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1691,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Notes (Remarks)\",\n                                                                        name: \"entries.\".concat(index, \".notes\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1698,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            form: form,\n                                                                            label: \"Documents Available\",\n                                                                            name: \"entries.\".concat(index, \".docAvailable\"),\n                                                                            options: [\n                                                                                {\n                                                                                    label: \"Invoice\",\n                                                                                    value: \"Invoice\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"BOL\",\n                                                                                    value: \"Bol\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"POD\",\n                                                                                    value: \"Pod\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"Packages List\",\n                                                                                    value: \"Packages List\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"Other Documents\",\n                                                                                    value: \"Other Documents\"\n                                                                                }\n                                                                            ],\n                                                                            className: \"flex-row gap-2 text-xs\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1705,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1704,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    (()=>{\n                                                                        var _formValues_entries;\n                                                                        const formValues = form.getValues();\n                                                                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                        const docAvailable = (entry === null || entry === void 0 ? void 0 : entry.docAvailable) || [];\n                                                                        const hasOtherDocuments = docAvailable.includes(\"Other Documents\");\n                                                                        return hasOtherDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: \"Specify Other Documents\",\n                                                                            name: \"entries.\".concat(index, \".otherDocuments\"),\n                                                                            type: \"text\",\n                                                                            isRequired: true,\n                                                                            placeholder: \"Enter other document types...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1734,\n                                                                            columnNumber: 33\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1743,\n                                                                            columnNumber: 33\n                                                                        }, undefined);\n                                                                    })()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1697,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1690,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    (()=>{\n                                                        var _formValues_entries;\n                                                        const formValues = form.getValues();\n                                                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                        const customFields = (entry === null || entry === void 0 ? void 0 : entry.customFields) || [];\n                                                        return Array.isArray(customFields) && customFields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"pt-3 border-t border-gray-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-purple-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1769,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: [\n                                                                                \"Custom Fields (\",\n                                                                                customFields.length,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1770,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1768,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                                    children: customFields.map((cf, cfIdx)=>{\n                                                                        const fieldType = cf.type || \"TEXT\";\n                                                                        const isAutoField = fieldType === \"AUTO\";\n                                                                        const autoOption = cf.autoOption;\n                                                                        let inputType = \"text\";\n                                                                        if (fieldType === \"DATE\" || isAutoField && autoOption === \"DATE\") {\n                                                                            inputType = \"date\";\n                                                                        } else if (fieldType === \"NUMBER\") {\n                                                                            inputType = \"number\";\n                                                                        }\n                                                                        const fieldLabel = isAutoField ? \"\".concat(cf.name, \" (Auto - \").concat(autoOption, \")\") : cf.name;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: fieldLabel,\n                                                                            name: \"entries.\".concat(index, \".customFields.\").concat(cfIdx, \".value\"),\n                                                                            type: inputType,\n                                                                            className: \"w-full\",\n                                                                            disable: isAutoField\n                                                                        }, cf.id, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1795,\n                                                                            columnNumber: 37\n                                                                        }, undefined);\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1774,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, \"custom-fields-\".concat(index, \"-\").concat(customFieldsRefresh), true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1764,\n                                                            columnNumber: 29\n                                                        }, undefined) : null;\n                                                    })(),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-3 border-t border-gray-100 mt-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-end space-x-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                        asChild: true,\n                                                                        tabIndex: -1,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 rounded-full flex items-center justify-center text-white font-bold text-xs cursor-help transition-colors duration-200 \".concat(filenameValidation[index] ? \"bg-green-500 hover:bg-green-600\" : \"bg-orange-500 hover:bg-orange-600\"),\n                                                                            tabIndex: -1,\n                                                                            role: \"button\",\n                                                                            \"aria-label\": \"Entry \".concat(index + 1, \" filename status\"),\n                                                                            children: \"!\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1817,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1816,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                        side: \"top\",\n                                                                        align: \"center\",\n                                                                        className: \"z-[9999]\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm max-w-md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium mb-1\",\n                                                                                    children: [\n                                                                                        \"Entry #\",\n                                                                                        index + 1,\n                                                                                        \" Filename\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1838,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                filenameValidation[index] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium text-green-600 mb-2\",\n                                                                                            children: \"Filename Generated\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1843,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs font-mono break-all bg-gray-100 p-2 rounded text-black\",\n                                                                                            children: generatedFilenames[index]\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1846,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1842,\n                                                                                    columnNumber: 37\n                                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium text-orange-600 mb-1\",\n                                                                                            children: \"Please fill the form to generate filename\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1852,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-600 mb-2\",\n                                                                                            children: \"Missing fields:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1856,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                            className: \"list-disc list-inside space-y-1\",\n                                                                                            children: (_missingFields_index = missingFields[index]) === null || _missingFields_index === void 0 ? void 0 : _missingFields_index.map((field, fieldIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                    className: \"text-xs\",\n                                                                                                    children: field\n                                                                                                }, fieldIndex, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1862,\n                                                                                                    columnNumber: 45\n                                                                                                }, undefined))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1859,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1851,\n                                                                                    columnNumber: 37\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1837,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1832,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1815,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1813,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1812,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1254,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, field.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1240,\n                                        columnNumber: 21\n                                    }, undefined);\n                                }),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            type: \"submit\",\n                                            className: \"px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg text-sm\",\n                                            children: \"Save\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                            lineNumber: 1887,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1886,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                    lineNumber: 1885,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                            lineNumber: 1234,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1233,\n                        columnNumber: 15\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                lineNumber: 1127,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n            lineNumber: 1126,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n        lineNumber: 1125,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreateTrackSheet, \"JO7kwYkLnkYHPQYrleMXiv9SYDw=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useWatch\n    ];\n});\n_c = CreateTrackSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreateTrackSheet);\nvar _c;\n$RefreshReg$(_c, \"CreateTrackSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/createTrackSheet.tsx\n"));

/***/ })

});