"use client";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import React, { useState } from "react";

type PageInputProps = {
  form: any;
  name: string;
  label: string;
  className?: string;
  isRequired?: boolean;
  isEntryPage?: boolean;
};

const PageInput = ({
  form,
  name,
  label,
  className,
  isRequired,
  isEntryPage,
}: PageInputProps) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => {
        // Parse the current value into two parts (if it exists)
        const [firstValue, secondValue] = field.value ? field.value.split(" of ") : ["", ""];

        // Local state for input values
        const [firstInput, setFirstInput] = useState(firstValue || "");
        const [secondInput, setSecondInput] = useState(secondValue || "");

        // Handle changes to the first input (any number)
        const handleFirstInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
          const inputValue = e.target.value;

          // Only allow numeric input
          if (inputValue === "" || /^\d+$/.test(inputValue)) {
            setFirstInput(inputValue);
            // Format the value for the form field
            if (inputValue && secondInput) {
              field.onChange(inputValue + " of " + secondInput);
            } else if (inputValue) {
              field.onChange(inputValue + " of ");
            } else if (secondInput) {
              field.onChange(" of " + secondInput);
            } else {
              field.onChange("");
            }
          }
        };

        // Handle changes to the second input (any number)
        const handleSecondInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
          const inputValue = e.target.value;

          // Only allow numeric input
          if (inputValue === "" || /^\d+$/.test(inputValue)) {
            setSecondInput(inputValue);
            // Format the value for the form field
            if (firstInput && inputValue) {
              field.onChange(firstInput + " of " + inputValue);
            } else if (firstInput) {
              field.onChange(firstInput + " of ");
            } else if (inputValue) {
              field.onChange(" of " + inputValue);
            } else {
              field.onChange("");
            }
          }
        };

        // Handle blur events to format the value correctly
        const handleFirstInputBlur = () => {
          if (firstInput && secondInput) {
            field.onChange(firstInput + " of " + secondInput);
          } else if (firstInput && !secondInput) {
            field.onChange(firstInput + " of ");
          }
        };

        const handleSecondInputBlur = () => {
          if (firstInput && secondInput) {
            field.onChange(firstInput + " of " + secondInput);
          } else if (!firstInput && secondInput) {
            field.onChange(" of " + secondInput);
          }
        };

        // No useEffect - we'll handle formatting in the change and blur handlers

        return (
          <FormItem
            className={cn`${
              isEntryPage ? "space-y-0.5 " : "md:mb-2 space-y-0.5"
            } ${className}`}
          >
            <FormLabel
              className={`${
                isEntryPage ? "md:text-xs" : "md:text-base"
              } text-gray-800 dark:text-gray-300 whitespace-nowrap cursor-text`}
            >
              {label}
              {isRequired && <span className="text-red-500">*</span>}
            </FormLabel>
            <FormControl>
              <div className="flex items-center gap-2">
                <Input
                  type="text"
                  value={firstInput}
                  onChange={handleFirstInputChange}
                  onBlur={handleFirstInputBlur}
                  className={cn(
                    "bg-gray-200 dark:bg-gray-700 border-none dark:border-gray-700 w-20 text-center",
                    isEntryPage ? "h-7 text-xs" : ""
                  )}
                />
                <span className="text-gray-600 dark:text-gray-400">of</span>
                <Input
                  type="text"
                  value={secondInput}
                  onChange={handleSecondInputChange}
                  onBlur={handleSecondInputBlur}
                  className={cn(
                    " bg-gray-200 dark:bg-gray-700 border-none dark:border-gray-700 w-20 text-center",
                    isEntryPage ? "h-7 text-xs" : ""
                  )}
                />
              </div>
            </FormControl>
            <FormMessage
              className={cn(
                isEntryPage ? "text-xs tracking-wider" : "tracking-wider"
              )}
            />
          </FormItem>
        );
      }}
    />
  );
};

export default PageInput;
