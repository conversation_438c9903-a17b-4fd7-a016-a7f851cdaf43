import DialogHeading from "@/app/_component/DialogHeading";
import FormCheckboxGroup from "@/app/_component/FormCheckboxGroup";
import FormInput from "@/app/_component/FormInput";
import PageInput from "@/app/_component/PageInput";
import SearchSelect from "@/app/_component/SearchSelect";
import SelectComp from "@/app/_component/SelectComp";
import TriggerButton from "@/app/_component/TriggerButton";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTrigger,
} from "@/components/ui/dialog";
import { SelectItem } from "@/components/ui/select";
import useDynamicForm from "@/lib/useDynamicForm";
import { useRouter } from "next/navigation";
import { useState, useEffect, useContext } from "react";
import { toast } from "sonner";
import { z } from "zod";
import { FormProvider } from "react-hook-form";
import { trackSheets_routes, clientCustomFields_routes } from "@/lib/routePath";
import { formSubmit, getAllData } from "@/lib/helpers";
import { TrackSheetContext } from "./TrackSheetContext";

const baseTrackSheetSchema = {
  clientId: z.string().optional(),
  company: z.string().optional(),
  division: z.string().optional(),
  invoice: z.string().optional(),
  masterInvoice: z.string().optional(),
  bol: z.string().optional(),
  invoiceDate: z.string().optional(),
  receivedDate: z.string().optional(),
  shipmentDate: z.string().optional(),
  carrierId: z.string().optional(),
  invoiceStatus: z.string().optional(),
  manualMatching: z.string().optional(),
  invoiceType: z.string().optional(),
  currency: z.string().optional(),
  qtyShipped: z.string().optional(),
  weightUnitName: z.string().optional(),
  quantityBilledText: z.string().optional(),
  invoiceTotal: z.string().optional(),
  savings: z.string().optional(),
  ftpFileName: z.string().optional(),
  ftpPage: z.string().optional(),
  docAvailable: z.array(z.string()).optional(),
  notes: z.string().optional(),
  mistake: z.string().optional(),
};

const UpdateTrackSheet = ({
  trackSheet,
  clientDataUpdate,
  carrierDataUpdate,
}: any) => {
  console.log(trackSheet);

  const [open, setOpen] = useState(false);
  const router = useRouter();
  const [customFieldsForClient, setCustomFieldsForClient] = useState<any[]>([]);
  const [customFieldsLoading, setCustomFieldsLoading] = useState(false);

  const parseCustomFields = () => {
    try {
      if (!trackSheet?.customFields) {
        return {};
      }

      if (
        typeof trackSheet.customFields === "object" &&
        !Array.isArray(trackSheet.customFields)
      ) {
        return trackSheet.customFields;
      }

      if (typeof trackSheet.customFields === "string") {
        try {
          return JSON.parse(trackSheet.customFields);
        } catch (e) {
          console.error("Error parsing custom fields string:", e);
          return {};
        }
      }

      return {};
    } catch (error) {
      console.error("Error parsing custom fields:", error);
      return {};
    }
  };

  const parsedCustomFields = parseCustomFields();

  const createDynamicSchema = () => {
    const schema = { ...baseTrackSheetSchema };
    if (customFieldsForClient && Array.isArray(customFieldsForClient)) {
      customFieldsForClient.forEach((field: any) => {
        const fieldName = `custom_${field.name}`;
        schema[fieldName] = field.required
          ? z.string().min(1, `${field.label} is required`)
          : z.string().optional();
      });
    }
    return z.object(schema);
  };
  const { setCustomFieldsReloadTrigger } = useContext(TrackSheetContext);

  const initialValues = {
    clientId: trackSheet?.client?.id?.toString() || "",
    company: trackSheet?.company || "",
    division: trackSheet?.division || "",
    invoice: trackSheet?.invoice?.toString() || "",
    masterInvoice: trackSheet?.masterInvoice?.toString() || "",
    bol: trackSheet?.bol?.toString() || "",
    invoiceDate: trackSheet?.invoiceDate?.split("T")[0] || "",
    receivedDate: trackSheet?.receivedDate?.split("T")[0] || "",
    shipmentDate: trackSheet?.shipmentDate?.split("T")[0] || "",
    carrierId: trackSheet?.carrier?.id?.toString() || "",
    invoiceStatus: trackSheet?.invoiceStatus || "",
    manualMatching: trackSheet?.manualMatching || "",
    invoiceType: trackSheet?.invoiceType || "",
    currency: trackSheet?.currency || "",
    qtyShipped: trackSheet?.qtyShipped?.toString() || "",
    weightUnitName: trackSheet?.weightUnitName || "",
    quantityBilledText: trackSheet?.quantityBilledText || "",
    invoiceTotal: trackSheet?.invoiceTotal?.toString() || "",
    savings: trackSheet?.savings || "",
    ftpFileName: trackSheet?.ftpFileName || "",
    ftpPage: trackSheet?.ftpPage || "",
    docAvailable: (() => {
      if (!trackSheet?.docAvailable) return [];
      if (Array.isArray(trackSheet.docAvailable))
        return trackSheet.docAvailable;
      try {
        const parsed = JSON.parse(trackSheet.docAvailable);
        return Array.isArray(parsed) ? parsed : [];
      } catch {
        return trackSheet.docAvailable.split(",").map((item) => item.trim());
      }
    })(),
    notes: trackSheet?.notes || "",
    mistake: trackSheet?.mistake || "",
  };

  if (customFieldsForClient && Array.isArray(customFieldsForClient)) {
    customFieldsForClient.forEach((field: any) => {
      const fieldName = `custom_${field.name}`;
      initialValues[fieldName] = field.value || "";
    });
  }

  const trackSheetSchema = createDynamicSchema();
  const { form } = useDynamicForm(trackSheetSchema, initialValues);

  useEffect(() => {
    // if (open && trackSheet?.client?.id) {
    setCustomFieldsLoading(true);
    getAllData(
      `${clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS}/${trackSheet.client.id}`
    )
      .then((res) => {
        if (res?.custom_fields) {
          const merged = res.custom_fields.map((field: any) => {
            const fieldValue = parsedCustomFields[field.id] || "";

            return {
              ...field,
              value: fieldValue,
            };
          });
          setCustomFieldsForClient(merged);

          // Reset form with custom field values
          if (merged && Array.isArray(merged)) {
            const customFieldValues: { [key: string]: string } = {};
            merged.forEach((field: any) => {
              const fieldName = `custom_${field.name}`;
              customFieldValues[fieldName] = field.value || "";
            });
            form.reset({ ...form.getValues(), ...customFieldValues });
          }
        } else {
          setCustomFieldsForClient([]);
        }
      })
      .catch((error) => {
        console.error("Error fetching custom fields:", error);
      })
      .finally(() => setCustomFieldsLoading(false));
    // }

    console.log("called");
  }, [open, parsedCustomFields, form]);

  async function onSubmit(values: any) {
    try {
      const customFieldsData: { [key: string]: string } = {};
      if (customFieldsForClient && Array.isArray(customFieldsForClient)) {
        customFieldsForClient.forEach((field: any) => {
          const fieldName = `custom_${field.name}`;
          if (values[fieldName] !== undefined) {
            customFieldsData[field.id] = values[fieldName];
          }
        });
      }

      const formData: any = {
        id: trackSheet.id,
        clientId: values.clientId,
        company: values.company,
        division: values.division,
        masterInvoice: values.masterInvoice,
        invoice: values.invoice,
        bol: values.bol,
        receivedDate: values.receivedDate,
        invoiceDate: values.invoiceDate,
        shipmentDate: values.shipmentDate,
        carrierId: values.carrierId,
        invoiceTotal: values.invoiceTotal,
        currency: values.currency,
        qtyShipped: values.qtyShipped,
        weightUnitName: values.weightUnitName,
        savings: values.savings,
        invoiceType: values.invoiceType,
        quantityBilledText: values.quantityBilledText,
        invoiceStatus: values.invoiceStatus,
        manualMatching: values.manualMatching,
        notes: values.notes,
        mistake: values.mistake,
        docAvailable: values.docAvailable ? values.docAvailable.join(",") : "",
        ftpFileName: values.ftpFileName,
        ftpPage: values.ftpPage,
        customFields: JSON.stringify(customFieldsData),
      };

      const res = await formSubmit(
        `${trackSheets_routes.UPDATE_TRACK_SHEETS}/${trackSheet.id}`,
        "PUT",
        formData
      );
      if (res.success) {
        toast.success(res.message);
        setOpen(false);
        router.refresh();
        form.reset();
        setCustomFieldsReloadTrigger((prev) => prev + 1);
      } else {
        toast.error(res.message || "Something went wrong");
      }
    } catch (error) {
      toast.error("An error occurred while updating the track sheet.");
      console.error(error);
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger title="Update">
        <TriggerButton type="edit" />
      </DialogTrigger>
      <DialogContent className="max-w-6xl dark:bg-gray-800 space-y-5 overflow-y-auto">
        <DialogHeader>
          <DialogHeading
            title="Update TrackSheet"
            description="Update TrackSheet details"
          />
        </DialogHeader>
        <FormProvider {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-5 gap-4">
              <SelectComp
                form={form}
                label="Client"
                name="clientId"
                placeholder="Select Client"
                isRequired
                disabled
              >
                {clientDataUpdate.map((client: any) => (
                  <SelectItem value={client?.id.toString()} key={client.id}>
                    {client.client_name}
                  </SelectItem>
                ))}
              </SelectComp>

              <FormInput
                form={form}
                label="FTP File Name"
                name="ftpFileName"
                type="text"
                isRequired
              />

              <PageInput
                className=""
                form={form}
                label="FTP Page"
                name="ftpPage"
                isRequired
              />

              <FormInput
                form={form}
                label="Company"
                name="company"
                type="text"
                isRequired
              />

              <FormInput
                form={form}
                className="mt-2"
                label="Division"
                name="division"
                type="text"
                placeholder="Enter Division"
              />

              <SelectComp
                form={form}
                label="Carrier"
                name="carrierId"
                placeholder="Select Carrier"
                isRequired
              >
                {carrierDataUpdate.map((carrier: any) => (
                  <SelectItem value={carrier?.id.toString()} key={carrier.id}>
                    {carrier.name}
                  </SelectItem>
                ))}
              </SelectComp>

              <FormInput
                form={form}
                label="Master Invoice"
                name="masterInvoice"
                type="text"
              />

              <FormInput
                form={form}
                label="Invoice"
                name="invoice"
                type="text"
                isRequired
              />

              <FormInput
                form={form}
                label="BOL"
                name="bol"
                type="text"
                isRequired
              />

              <FormInput
                form={form}
                label="Received Date"
                name="receivedDate"
                type="date"
                isRequired
              />

              <FormInput
                form={form}
                label="Invoice Date"
                name="invoiceDate"
                type="date"
                isRequired
              />

              <FormInput
                form={form}
                label="Shipment Date"
                name="shipmentDate"
                type="date"
                isRequired
              />

              <FormInput
                className="mt-2"
                form={form}
                label="Invoice Total"
                name="invoiceTotal"
                type="text"
              />

              <SearchSelect
                form={form}
                name="currency"
                label="Currency"
                placeholder="Search currency"
                isRequired
                options={[
                  { value: "USD", label: "USD" },
                  { value: "CAD", label: "CAD" },
                  { value: "EUR", label: "EUR" },
                ]}
              />

              <FormInput
                form={form}
                className="mt-2"
                label="Quantity Shipped"
                name="qtyShipped"
                type="text"
              />

              <FormInput
                form={form}
                className="mt-2"
                label="Weight Unit"
                name="weightUnitName"
                type="text"
                isRequired
              />

              <FormInput
                form={form}
                label="Savings"
                name="savings"
                type="text"
              />

              <FormInput
                form={form}
                label="Invoice Type"
                name="invoiceType"
                type="text"
                isRequired
              />

              <FormInput
                form={form}
                label="Quantity Billed Text"
                name="quantityBilledText"
                type="text"
              />

              <FormInput
                form={form}
                label="Invoice Status"
                name="invoiceStatus"
                type="text"
                isRequired
              />

              <FormInput
                form={form}
                label="Manual Matching"
                name="manualMatching"
                type="text"
                isRequired
              />

              <FormInput form={form} label="Notes" name="notes" type="text" />

              <FormInput
                form={form}
                label="Mistake"
                name="mistake"
                type="text"
              />

              <FormCheckboxGroup
                form={form}
                label="Documents Available"
                name="docAvailable"
                options={[
                  { label: "Invoice", value: "Invoice" },
                  { label: "BOL", value: "Bol" },
                  { label: "POD", value: "Pod" },
                  { label: "Packages List", value: "Packages List" },
                  { label: "Other Documents", value: "Other Documents" },
                ]}
                className="flex-row gap-2 text-xs"
              />

              {customFieldsLoading ? (
                <div className="col-span-5">Loading custom fields...</div>
              ) : (
                customFieldsForClient &&
                customFieldsForClient.length > 0 &&
                customFieldsForClient.map((field: any) => {
                  const fieldName = `custom_${field.name}`;
                  return (
                    <FormInput
                      key={field.id}
                      form={form}
                      label={field.label || field.name}
                      name={fieldName}
                      type={
                        field.type === "DATE"
                          ? "date"
                          : field.type === "NUMBER"
                          ? "number"
                          : "text"
                      }
                      isRequired={field.required}
                      disable={field.type === "AUTO"}
                      placeholder={`Enter ${field.label || field.name}`}
                    />
                  );
                })
              )}
            </div>
            <DialogFooter>
              <button
                type="submit"
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                Update
              </button>
            </DialogFooter>
          </form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
};

export default UpdateTrackSheet;
