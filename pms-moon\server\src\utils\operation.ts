import bcrypt from "bcrypt";
import { checkExistingRow, DeleteMany, findMany, handleError } from "./helpers";
import { Request, Response } from "express";
import prisma from "./prismaClient"

export const createItem = async ({
  model,
  fieldName,
  fields,
  res,
  req,
  successMessage,
}) => {
  try {
    let completeField = { ...fields };

    const criteria = Object.fromEntries(
      Object.entries({
        ...completeField,
      })
    );

    const errorMessage = `${model} already exists`;
    const existingRowerror = await checkExistingRow({
      model,
      criteria,
      res,
      errorMessage,
    });

    if (existingRowerror) return existingRowerror;

    if (completeField.password) {
      const hashedPassword = bcrypt.hashSync(completeField.password, 10);
      completeField.password = hashedPassword;
    }

    const data = await (prisma as any)[model].create({
      data: completeField,
    });
    return res.status(201).json({ success: true, message: successMessage });
  } catch (error) {
    return handleError(res, error);
  }
};
export const updateItemTrackSheet = async ({
  model,
  fieldName,
  fields,
  id,
  res,
  req,
  successMessage,
}) => {
  try {
    const currentRecord = await (prisma as any)[model].findUnique({
      where: {
        [fieldName]: id,
      },
    });

    if (!currentRecord) {
      return res.status(404).json({
        success: false,
        message: `${model} doesn't exist`,
      });
    }

    const uniqueFieldsToCheck: string[] = ['invoice']; 

    for (const uniqueField of uniqueFieldsToCheck) {
      if (fields.hasOwnProperty(uniqueField) && fields[uniqueField] !== currentRecord[uniqueField]) {
        const existingRecordWithNewValue = await (prisma as any)[model].findFirst({
          where: {
            AND: [
              { [fieldName]: { not: id } },
              { [uniqueField]: fields[uniqueField] },
            ],
          },
        });

        if (existingRecordWithNewValue) {
          return res
            .status(400)
            .json({ 
              success: false, 
              message: `${uniqueField} with value "${fields[uniqueField]}" already exists` 
            });
        }
      }
    }

    const result = await (prisma as any)[model].update({
      where: {
        [fieldName]: id,
      },
      data: fields,
    });

    return res.status(200).json({
      success: true,
      message: successMessage,
      data: result,
    });
  } catch (error) {
    console.error('Error in updateItemTrackSheet:', error);
    return handleError(res, error);
  }
};

export const updateItem = async ({
  model,
  fieldName,
  fields,
  id,
  res,
  req,
  successMessage,
}) => {
  try {
    const currentRecord = await (prisma as any)[model].findUnique({
      where: {
        [fieldName]: id,
      },
    });

    if (!currentRecord) {
      return res.status(404).json({
        success: false,
        message: `${model} doesn't exist`,
      });
    }

    const completeField = { ...fields };
    const criteria = Object.fromEntries(
      Object.entries({
        ...completeField,
      })
    );

    const isChanged = Object.keys(fields).some(
      (key) => fields[key] !== currentRecord[key]
    );
    if (isChanged) {
      let existingRecods;

      if (completeField.username || completeField.email) {
        existingRecods = await (prisma as any)[model].findFirst({
          where: {
            AND: [
              { [fieldName]: { not: id } },
              { OR: [{username: completeField.username },{email: completeField.email}]}
            ],
          },
        });
      } else {
        existingRecods = await (prisma as any)[model].findFirst({
          where: {
            AND: [{ [fieldName]: { not: id } }, criteria],
          },
        });
      }

      if (existingRecods) {
        return res
          .status(400)
          .json({ success: false, message: `${model} already exist` });
      }
    }

    const result = await (prisma as any)[model].update({
      where: {
        [fieldName]: id,
      },
      data: {
        ...fields,
      },
    });
    return res.status(200).json({
      success: true,
      message: successMessage,
      data: result,
    });
  } catch (error) {
    console.log(error)
    return handleError(res, error);
  }
};

export const deleteItem = async ({
  model,
  fieldName,
  id,
  res,
  req,
  successMessage,
}) => {
  try {
    const existingRecord = await (prisma as any)[model].findUnique({
      where: { [fieldName]: id },
    });
    if (!existingRecord) {
      return res
        .status(400)
        .json({ success: false, message: "No such Record" });
    }

    await (prisma as any)[model].delete({
      where: { [fieldName]: id },
    });
    return res.status(200).json({ success: true, message: successMessage });
  } catch (error) {
    return handleError(res, error);
  }
};
export const getItem = async ({
  model,
  params,
  res,
}: {
  model: any;
  params: any;
  res: Response;
}) => {
  return await findMany({
    model,
    params,
    res,
    // postProcess: async (data) => await enrichWithAuditLogs(data, model),
  });
};

export const createTransactions = async ({
  model01,
  fieldName01,
  fields01,
  logging_relationship01,
  model02,
  fieldName02,
  fields02,
  logging_relationship02,
  model03,
  fieldName03,
  fields03,
  logging_relationship03,
  res,
  req,
  successMessage,
}: {
  res: Response;
  req: any;
  model01: string;
  fieldName01: string;
  fields01: Record<string, any>;
  logging_relationship01: string;
  model02: string;
  fieldName02: string;
  fields02: Record<string, any>[];
  logging_relationship02: string;
  model03?: string;
  fieldName03?: string;
  fields03?: Record<string, any>;
  logging_relationship03?: string;
  successMessage: string;
}) => {
  try {
    const { freightadminid, freightuserid } = req;
    const user_role =
      freightadminid && freightuserid ? "FREIGHT_ADMIN_USER" : "FREIGHT_ADMIN";
    const userId =
      freightadminid && freightuserid ? freightuserid : freightadminid;

    await prisma.$transaction(async (tx) => {
      const result = await tx[model01].create({
        data: {
          ...fields01,
        },
      });
      const id = result[fieldName01];

      for (let i = 0; i < fields02.length; i++) {
        await tx[model02].create({
          data: {
            ...fields02[i],
            [fieldName01]: id,
          },
        });
      }

      return res.status(200).json({
        success: true,
        message: successMessage,
      });
    });
  } catch (error) {
    handleError(res, error);
  }
};

export const updateTransaction = async ({
  model01,
  fieldName01,
  fields01,
  logging_relationship01,
  model02,
  fieldName02,
  fields02,
  logging_relationship02,
  model03,
  fieldName03,
  fields03,
  logging_relationship03,
  res,
  req,
  successMessage,
}: {
  res: Response;
  req: any;
  model01: string;
  fieldName01: string;
  fields01: Record<string, any>;
  logging_relationship01: string;
  model02: string;
  fieldName02: string;
  fields02: Record<string, any>[];
  logging_relationship02: string;
  model03?: string;
  fieldName03?: string;
  fields03?: Record<string, any>;
  logging_relationship03?: string;
  successMessage: string;
}) => {
  try {
    const { freightadminid, freightuserid } = req;
    const user_role =
      freightadminid && freightuserid ? "FREIGHT_ADMIN_USER" : "FREIGHT_ADMIN";
    const userId =
      freightadminid && freightuserid ? freightuserid : freightadminid;
    await prisma.$transaction(async (tx) => {
      const result = await tx[model01].update({
        data: {
          ...fields01,
        },
        where: {
          [fieldName01]: fields01[fieldName01],
        },
      });
      const id = result[fieldName01];

      await DeleteMany({
        model: model02,
        where_condition: { [fieldName01]: id },
        res,
      });
      for (let i = 0; i < fields02.length; i++) {
        await tx[model02].create({
          data: {
            ...fields02[i],
            [fieldName01]: id,
          },
        });
      }

      return res.status(200).json({
        success: true,
        message: successMessage,
      });
    });
  } catch (error) {
    handleError(res, error);
  }
};

export const createManyItems = async ({
  model,
  fieldsArray, // Array of field objects
  res,
  req,
  successMessage,
}) => {
  try {
    const createdItems = []; // Store created items

    for (const fields of fieldsArray) {
      let completeField = { ...fields };

      const criteria = Object.fromEntries(
        Object.entries({
          ...completeField,
        })
      );

      const errorMessage = `${model} already exists`;
      const existingRowError = await checkExistingRow({
        model,
        criteria,
        res,
        errorMessage,
      });

      if (existingRowError) {
        return existingRowError; // Exit early if an existing row is found
      }

      if (completeField.password) {
        const hashedPassword = bcrypt.hashSync(completeField.password, 10);
        completeField.password = hashedPassword;
      }

      const data = await (prisma as any)[model].create({
        data: completeField,
      });
      createdItems.push(data); // Add created item to the results array
    }

    return res
      .status(200)
      .json({ success: true, message: successMessage, createdItems });
  } catch (error) {
    return handleError(res, error);
  }
};

export const createItemCustomFields = async ({
  model,
  fieldName,
  fields,
  res,
  req,
  successMessage,
}) => {
  try {
    let completeField = { ...fields };

    // Exclude customFields from criteria to avoid Prisma JSON filter error
    const { customFields, ...criteriaFields } = completeField;
    const criteria = Object.fromEntries(
      Object.entries({
        ...criteriaFields,
      })
    );

    const errorMessage = `${model} already exists`;
    const existingRowerror = await checkExistingRow({
      model,
      criteria,
      res,
      errorMessage,
    });

    if (existingRowerror) return existingRowerror;

    if (completeField.password) {
      const hashedPassword = bcrypt.hashSync(completeField.password, 10);
      completeField.password = hashedPassword;
    }

    const data = await (prisma as any)[model].create({
      data: completeField,
    });
    return res.status(201).json({ success: true, message: successMessage });
  } catch (error) {
    return handleError(res, error);
  }
};

export const createManyItemsCustomFields = async ({
  model,
  fieldsArray, // Array of field objects
  res,
  req,
  successMessage,
}) => {
  try {
    const createdItems = []; // Store created items

    for (const fields of fieldsArray) {
      let completeField = { ...fields };

      // Exclude customFields from criteria to avoid Prisma JSON filter error
      const { customFields, ...criteriaFields } = completeField;
      const criteria = Object.fromEntries(
        Object.entries({
          ...criteriaFields,
        })
      );

      const errorMessage = `${model} already exists`;
      const existingRowError = await checkExistingRow({
        model,
        criteria,
        res,
        errorMessage,
      });

      if (existingRowError) {
        return existingRowError; // Exit early if an existing row is found
      }

      if (completeField.password) {
        const hashedPassword = bcrypt.hashSync(completeField.password, 10);
        completeField.password = hashedPassword;
      }

      const data = await (prisma as any)[model].create({
        data: completeField,
      });
      createdItems.push(data); // Add created item to the results array
    }

    return res
      .status(200)
      .json({ success: true, message: successMessage, createdItems });
  } catch (error) {
    return handleError(res, error);
  }
};
