"use client";
import React, { useState, useEffect, useCallback } from "react";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import Select from "react-select";
import axios from "axios";
import { getAllData } from "@/lib/helpers";
import { client_routes } from "@/lib/routePath";
import { toast } from "sonner";

import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  rectSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { TouchSensor } from "@dnd-kit/core";


const sanitize = (name: string) =>
  name
    .toLowerCase()
    .replace(/\s+/g, "-")
    .replace(/[^a-z0-9-_]/g, "");

type ClientOption = { value: string; label: string };
type CustomField = { id: string; displayName: string; fieldtype?: string };

const SortableItem = ({
  id,
  displayName,
  fieldtype,
  isSelected,
  onSelect,
}: {
  id: string;
  displayName: string;
  fieldtype?: string;
  isSelected: boolean;
  onSelect: (id: string) => void;
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id,
    data: {
      type: "custom-field",
      id,
    },
  });

  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 999 : 1,
  };

  const displayedFieldtype = (() => {
    if (!fieldtype) return "Text";

    // Handle auto fields with specific format "auto-[option]"
    if (fieldtype.startsWith("auto-")) {
      const autoOption = fieldtype.replace("auto-", "");
      switch (autoOption.toLowerCase()) {
        case "username":
          return "Auto - Username";
        case "date":
          return "Auto - Date";
        case "xyz":
          return "Auto - XYZ";
        default:
          return `Auto - ${
            autoOption.charAt(0).toUpperCase() +
            autoOption.slice(1).toLowerCase()
          }`;
      }
    }

    // Handle regular field types
    switch (fieldtype.toLowerCase()) {
      case "text":
        return "Text";
      case "number":
        return "Number";
      case "date":
        return "Date";
      case "auto":
        return "Auto";
      default:
        return (
          fieldtype.charAt(0).toUpperCase() + fieldtype.slice(1).toLowerCase()
        );
    }
  })();

  return (
    <div
      ref={setNodeRef}
      style={{ ...style, touchAction: "none" }}
      {...attributes}
      {...listeners}
      className={`
        flex flex-col cursor-grab active:cursor-grabbing select-none
        transition-all duration-200 group
        ${isDragging ? "scale-105" : "hover:scale-[1.02]"}
      `}
      onClick={() => onSelect(id)}
    >
      {/* Field Name Header - Above the tile */}
      <div className="mb-1 px-1">
        <h3 className="text-gray-900 font-bold text-sm leading-tight truncate">
          {displayName}
        </h3>
      </div>

      {/* Main Tile */}
      <div
        className={`
          relative p-2 rounded-lg flex items-center justify-start
          min-h-[30px] max-w-[200px] border border-gray-300 shadow-sm
          transition-all duration-200
          ${
            isSelected
              ? "border-gray-500 shadow-md"
              : "group-hover:border-gray-400 group-hover:shadow-md"
          }
          ${isDragging ? "shadow-xl border-gray-500" : ""}
        `}
        style={{ backgroundColor: '#e5e7eb' }}
      >
        {/* Field Type - Inside the tile */}
        <span
          className="text-gray-600 text-sm font-medium text-left truncate"
        >
          {displayedFieldtype}
        </span>

        {/* Selection Indicator */}
        {isSelected && (
          <div className="absolute top-2 right-2">
            <div className="w-5 h-5 bg-gray-600 rounded-full flex items-center justify-center">
              <svg
                width="10"
                height="10"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M20 6L9 17L4 12"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const ArrangementPage = () => {
  const [clients, setClients] = useState<ClientOption[]>([]);
  const [selectedClient, setSelectedClient] = useState<string | null>(null);
  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  const [reorderedFields, setReorderedFields] = useState<CustomField[]>([]);
  const [originalFields, setOriginalFields] = useState<CustomField[]>([]); // Store original order
  const [selectedFieldIds, setSelectedFieldIds] = useState<Set<string>>(
    new Set()
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasReordered, setHasReordered] = useState(false); // Track if user has reordered

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250,
        tolerance: 5,
      },
    })
  );

  useEffect(() => {
    const fetchClients = async () => {
      try {
        const res = await getAllData(client_routes.GETALL_CLIENT);
        const clientOptions = res?.data.map((client: any) => ({
          value: client.id,
          label: client.client_name,
        }));
        setClients(clientOptions);
      } catch (err) {
        console.error("Failed to fetch clients", err);
      }
    };
    fetchClients();
  }, []);

  const fetchCustomFields = useCallback(async () => {
    if (!selectedClient) return;
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/client-custom-fields/${selectedClient}`
      );
      const customFieldsArray = res.data.custom_fields || [];
      console.log("Fetched custom fields:", customFieldsArray);
      console.log("Sample field structure:", customFieldsArray[0]);
      const customFieldObjects: CustomField[] = customFieldsArray.map(
        (field: any) => {
          const name =
            typeof field === "string"
              ? field
              : field.name || field.displayName || field.label || "";

          // Handle field type properly - check for 'type' field from backend
          let fieldtype = "";
          if (typeof field !== "string") {
            if (field.type === "AUTO" && field.autoOption) {
              // If it's an auto field, show "Auto - [AutoOption]"
              fieldtype = `auto-${field.autoOption.toLowerCase()}`;
            } else {
              // Otherwise use the type or fallback to fieldtype
              fieldtype = field.type || field.fieldtype || "";
            }
          }

          return {
            id: typeof field === "string" ? field : field.id,
            displayName: name,
            fieldtype,
          };
        }
      );

      // The backend should already return fields in the correct order
      // So we use the order as received from the backend
      setCustomFields(customFieldObjects);
      setReorderedFields(customFieldObjects);
      setOriginalFields(customFieldObjects); // Store original order for reset functionality
      setSelectedFieldIds(new Set());
      setHasReordered(false); // Reset reorder status when new client is selected
    } catch (err) {
      console.error("Failed to fetch custom fields", err);
    }
  }, [selectedClient]);

  useEffect(() => {
    if (selectedClient) {
      fetchCustomFields();
    } else {
      setCustomFields([]);
      setReorderedFields([]);
      setOriginalFields([]);
      setSelectedFieldIds(new Set());
      setHasReordered(false);
    }
  }, [selectedClient, fetchCustomFields]);

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (over && active.id !== over.id) {
      const oldIndex = reorderedFields.findIndex(
        (item) => item.id === active.id
      );
      const newIndex = reorderedFields.findIndex((item) => item.id === over.id);
      setReorderedFields((items) => arrayMove(items, oldIndex, newIndex));
      setHasReordered(true); // Mark that user has reordered fields
    }
  };

  const toggleSelectField = (id: string) => {
    setSelectedFieldIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) newSet.delete(id);
      else newSet.add(id);
      return newSet;
    });
  };

  const handleReset = () => {
    setReorderedFields(originalFields);
    setHasReordered(false);
    setSelectedFieldIds(new Set());
  };

  const handleSubmit = async () => {
    if (!selectedClient) return;
    setIsSubmitting(true);

    const orderedIds = reorderedFields.map((field) => field.id);
    console.log("Saving order:", orderedIds);

    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/client-custom-fields/order`,
        {
          client_id: selectedClient,
          custom_fields: orderedIds,
        }
      );
      console.log("Save response:", response.data);
      toast.success("Custom field order updated successfully!");
    } catch (err) {
      console.error("Failed to update custom field order", err);
      toast.error("Failed to save order.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full px-6 pb-12 min-h-screen bg-gradient-to-br from-slate-100 via-gray-50 to-blue-50">
      <div className="h-12 flex items-center">
        <AdminNavBar
          link="/pms/arrange_custom_fields"
          name="Arrange Custom Fields"
        />
      </div>

      <div className="space-y-3 mt-6 max-w-3xl mx-auto text-center">
        <h1 className="text-4xl font-extrabold text-gray-900 tracking-tight">
          Arrange Custom Fields
        </h1>
        <p className="text-lg text-gray-600 max-w-md mx-auto">
          Select a client and drag fields below to reorder.
        </p>
      </div>

      <div className="h-10 max-w-sm mx-auto mt-8 relative">
        {/* Gradient glow background */}
        <div className="absolute inset-0 rounded-[1.75rem] bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 opacity-75 blur-sm"></div>

        {/* Main searchbar container */}
        <div className="relative h-full bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-[1.75rem] p-[1px]">
          <div className="h-full rounded-[1.75rem] bg-white shadow-lg">
            <Select
              options={clients}
              onChange={(option) => setSelectedClient(option?.value || null)}
              placeholder="Select a client..."
              isSearchable
              menuPortalTarget={
                typeof document !== "undefined" ? document.body : undefined
              }
              menuPosition="fixed"
              styles={{
                control: (provided) => ({
                  ...provided,
                  backgroundColor: "transparent",
                  border: "none",
                  boxShadow: "none",
                  minHeight: "40px",
                  cursor: "pointer",
                  borderRadius: "1.75rem",
                }),
                container: (provided) => ({
                  ...provided,
                  width: "100%",
                }),
                menu: (provided) => ({
                  ...provided,
                  zIndex: 9999,
                }),
              }}
            />
          </div>
        </div>
      </div>

      {/* No Fields Message */}
      {selectedClient && reorderedFields.length === 0 && (
        <div className="max-w-2xl mx-auto mt-16 text-center">
          <div className="bg-gradient-to-br from-slate-50 to-gray-100 rounded-3xl p-12 border-2 border-dashed border-gray-300">
            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="text-gray-400"
              >
                <path
                  d="M9 12H15M9 16H15M17 21H7C5.89543 21 5 20.1046 5 19V5C5 3.89543 5.89543 3 7 3H12.5858C12.851 3 13.1054 3.10536 13.2929 3.29289L19.7071 9.70711C19.8946 9.89464 20 10.149 20 10.4142V19C20 20.1046 19.1046 21 18 21H17Z"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No Custom Fields Found
            </h3>
            <p className="text-gray-600 mb-6">
              This client doesn't have any custom fields to arrange yet.
            </p>
            {/* <button className="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-xl font-medium transition-all duration-200 hover:scale-105">
              Add Custom Fields
            </button> */}
          </div>
        </div>
      )}

      {selectedClient && reorderedFields.length > 0 && (
        <div className="max-w-2xl mx-auto mt-10">
          {/* Header Section */}
          <div className="mb-8 text-center relative">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Customize Field Order
            </h2>
            <p className="text-gray-600">
              Drag and drop the fields below to arrange them in your preferred
              order
            </p>

            {/* Help Tooltip - Top Right */}
            <div className="absolute top-0 right-0 group">
              <div className="w-9 h-9 bg-white border border-gray-300 hover:border-gray-400 rounded-full flex items-center justify-center cursor-help transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md">
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="text-gray-500 hover:text-gray-700"
                >
                  <circle
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="2"
                  />
                  <path
                    d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M12 17h.01"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>

              {/* Tooltip Content - Appears Upwards */}
              <div className="absolute bottom-12 right-0 w-64 bg-white border border-gray-200 rounded-lg shadow-xl p-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-[9999] transform group-hover:translate-y-0 translate-y-2">
                {/* Arrow pointing down */}
                <div className="absolute -bottom-1 right-4 w-3 h-3 bg-white border-r border-b border-gray-200 transform rotate-45"></div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-800 text-sm mb-2">
                    How to use:
                  </h4>
                  <div className="space-y-1.5 text-xs text-gray-600">
                    <div className="flex items-center gap-2">
                      <span className="w-1.5 h-1.5 bg-gray-400 rounded-full flex-shrink-0"></span>
                      <span>
                        <strong className="text-gray-700">Drag</strong> tiles to
                        reorder
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="w-1.5 h-1.5 bg-gray-400 rounded-full flex-shrink-0"></span>
                      <span>
                        <strong className="text-gray-700">Click</strong> to
                        select/deselect
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="w-1.5 h-1.5 bg-gray-400 rounded-full flex-shrink-0"></span>
                      <span>
                        <strong className="text-gray-700">Reset</strong> to
                        restore original order
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="w-1.5 h-1.5 bg-gray-400 rounded-full flex-shrink-0"></span>
                      <span>
                        <strong className="text-gray-700">Save</strong> to make
                        permanent
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={reorderedFields.map((item) => item.id)}
              strategy={rectSortingStrategy}
            >
              {/* Improved Container */}
              <div className="relative">
                <div
                  className={`
                    grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3
                    gap-4 p-4 rounded-3xl border-2 border-dashed border-gray-300
                    bg-gradient-to-br from-white via-slate-50 to-gray-50
                    shadow-inner transition-all duration-300
                    ${reorderedFields.length === 0 ? "min-h-[200px]" : ""}
                  `}
                  style={{
                    backgroundImage: `
                      radial-gradient(circle at 25px 25px, rgba(107, 114, 128, 0.1) 2px, transparent 0),
                      radial-gradient(circle at 75px 75px, rgba(107, 114, 128, 0.05) 2px, transparent 0)
                    `,
                    backgroundSize: "100px 100px",
                  }}
                >
                  {reorderedFields.map((field, index) => (
                    <div
                      key={field.id}
                      className="transform transition-all duration-200"
                      style={{
                        animationDelay: `${index * 50}ms`,
                      }}
                    >
                      <SortableItem
                        id={field.id}
                        displayName={field.displayName}
                        fieldtype={field.fieldtype}
                        isSelected={selectedFieldIds.has(field.id)}
                        onSelect={toggleSelectField}
                      />
                    </div>
                  ))}
                </div>

                {/* Drop Zone Indicator */}
                <div className="absolute inset-0 pointer-events-none">
                  <div className="w-full h-full rounded-3xl border-2 border-transparent transition-all duration-300" />
                </div>
              </div>
            </SortableContext>
          </DndContext>

          {/* Action Buttons */}
          <div className="mt-10 flex justify-center gap-4">
            <button
              onClick={handleReset}
              disabled={!hasReordered}
              className={`
                px-6 py-3 rounded-xl font-medium transition-all duration-200
                ${
                  hasReordered
                    ? "bg-gray-100 hover:bg-gray-200 text-gray-700 hover:scale-105"
                    : "bg-gray-50 text-gray-400 cursor-not-allowed"
                }
              `}
            >
              Reset
            </button>
            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className={`
                px-8 py-3 rounded-xl font-semibold transition-all duration-200
                ${
                  isSubmitting
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 hover:scale-105 shadow-lg hover:shadow-xl"
                }
                text-white transform
              `}
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Saving...
                </div>
              ) : (
                "Save Order"
              )}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ArrangementPage;