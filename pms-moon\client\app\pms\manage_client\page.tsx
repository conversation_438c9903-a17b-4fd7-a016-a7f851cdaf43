import React from "react";
import AddClient from "./addClient";
import {
  getAllData,
  getCookie,
  hasPermission,
  PermissionWrapper,
} from "@/lib/helpers";
import {
  associate_routes,
  branch_routes,
  carrier_routes,
  client_routes,
  employee_routes,
  search_routes,
} from "@/lib/routePath";
import { ViewClient } from "./ViewClient";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import AddCustomField from "../addupdate_custom_fields/AddCustomField";
import ArrangementButton from "../arrange_custom_fields/ArrangementButton";

const ClientPage = async ({
  searchParams,
}: {
  searchParams: {
    pageSize?: string;
    page?: string;
    "Client Name"?: string;
    Ownership: string;
    Associate: string;
    Branch: string;
  };
}
) => {
  const {
    page = "1",
    pageSize = "50",
    ["Client Name"]: ClientName,
    Associate,
    Ownership,
    Branch,
  } = searchParams;

  const params = new URLSearchParams();

  if (pageSize) params.append("pageSize", pageSize);
  if (page) params.append("page", page);
  if (ClientName) params.append("client_name", ClientName);
  if (Associate) params.append("associate.name", Associate);
  if (Ownership) params.append("ownership.username", Ownership);
  if (Branch) params.append("branch.branch_name", Branch);
  params.append("includeRelations", "ownership, branch, associate");
  const pageSizes = 50;
  const pages = 1;
  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  // const API_URL = `${carrier_routes.GETALL_CARRIER}?pageSize=${pageSizes}&page=${pages}`;
  // const allCarrier = await getAllData(API_URL);
  const allBranch = await getAllData(branch_routes.GETALL_BRANCH);

  const allAssociate = await getAllData(associate_routes.GETALL_ASSOCIATE);  

  const allUsers = await getAllData(employee_routes.GETALL_USERS);
  const allUser = allUsers?.data;
  const userPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.action
    ) || [];
  //console.log(userData)
  const corporationCookie = await getCookie("corporationtoken");
  // If corporationCookie doesn't exist, manually remove 'allow_all' from permissions
  // let permissions = userPermissions;
  const permissions = corporationCookie ? ["allow_all"] : userPermissions;
  // Only add 'allow_all' if corporationCookie exists
  const api_url = `${search_routes.GET_SEARCH}/client?${params?.toString()}`;
  console.time("Time taken for" + api_url);
  const allClient = await getAllData(api_url);
  console.timeEnd("Time taken for" + api_url);


  return (
    <>
      <div className="w-full pl-4">
        <div className="h-9 flex items-center">
          <AdminNavBar link={"/pms/manage_client"} name={"Manage Client"} />
        </div>
        <div className="space-y-2 ">
          <h1 className="text-2xl">Manage Client</h1>
          <p className="text-sm text-gray-700">Here you can manage client</p>
        </div>
        <div className="w-full pr-3">
          <div className="flex justify-end items-center gap-2 mt-4">
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["create-client"]}
            >
              <AddClient
                data={allClient}
                allBranch={allBranch}
                allUser={allUser}
                params={params}
                allAssociate={allAssociate.data}
              />
            </PermissionWrapper>
          </div>
          <div className="w-full py-4 animate-in fade-in duration-1000">
            {/* {hasPermission({
              permission_data: permissions,
              permission: "view"
            }) && } */}
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["view-client"]}
            >
              <ViewClient
                alldata={allClient}
                permissions={permissions}
                // allCarrier={allCarrier?.data}
                allBranch={allBranch}
                allUser={allUser}
                allAssociate={allAssociate.data}
                userData={userData}
              />
            </PermissionWrapper>
          </div>
        </div>
      </div>
    </>
  );
};

export default ClientPage;
