import { useContext } from "react";
import { TrackSheetContext } from "./TrackSheetContext";
import Column from "./column";
import DataGridTableTrackSheet from "@/app/_component/DataGridTableTrackSheet";

const ViewTrackSheet = ({
  permissions,
  totalPages,
  customFieldsMap,
  selectedClients,
  trackSheetData,
  pageSize,
  carrier,
  associate,
  client,
  carrierDataUpdate,
  clientDataUpdate
}: any) => {
  
  const { deleteData, setDeletedData } = useContext(TrackSheetContext);

  return (
    <div className="w-full">
      {selectedClients.length === 0 ? (
        <div className="flex items-center justify-center h-64">
          <p className="text-gray-500 text-lg">Please Select a Client</p>
        </div>
      ) : (
        <DataGridTableTrackSheet
          data={trackSheetData.data}
          columns={Column(permissions, setDeletedData, deleteData,associate,carrierDataUpdate, clientDataUpdate,{
            customFieldsMap: customFieldsMap,
          }
        )
        }
          customFieldsMap={customFieldsMap}
          showColDropDowns
          showPageEntries
          className="w-full"
          total={true}
          pageSize={pageSize}
          totalPages={totalPages}
        />
      )}
    </div>
  );
};

export default ViewTrackSheet;
