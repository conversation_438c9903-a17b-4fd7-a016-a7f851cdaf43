import {
  FaUserPlus,
  FaUserCircle,
  FaTruck,
  FaBuilding,
  FaBriefcase,
  FaShieldAlt,
  FaFileSignature,
  FaPenFancy,
  FaGripHorizontal,
} from "react-icons/fa";
import { RiUserFill } from "react-icons/ri";
import { BiSolidCategory } from "react-icons/bi";
import { TbReportSearch } from "react-icons/tb";

export const managementRoutes = [
  {
    label: "Manage User",
    path: "/pms/manage_employee",
    permission: { module: "USER MANAGEMENT" },
    icon: <FaUserPlus />,
  },
  {
    label: "Manage Client",
    path: "/pms/manage_client",
    permission: { module: "CLIENT MANAGEMENT", action: "create-client" },
    icon: <FaUserCircle />,
  },
  {
    label: "Manage Carrier",
    path: "/pms/manage_carrier",
    permission: { module: "CARRIER MANAGEMENT" },
    icon: <FaTruck />,
  },
  {
    label: "Manage Category",
    path: "/pms/manage_category",
    permission: { module: "CATEGORY MANAGEMENT" },
    icon: <BiSolidCategory />,
  },
  {
    label: "Manage Branch",
    path: "/pms/manage_branch",
    permission: { module: "BRANCH MANAGEMENT" },
    icon: <FaBuilding />,
  },
  {
    label: "Manage Associate",
    path: "/pms/manage_associate",
    permission: { module: "ASSOCIATE MANAGEMENT" },
    icon: <RiUserFill />,
  },
  {
    label: "Manage Work Type",
    path: "/pms/manage_work_type",
    permission: { module: "WORKTYPE MANAGEMENT", action: "update-work" },
    icon: <FaBriefcase />,
  },
  {
    label: "Manage Roles",
    path: "/pms/manage-roles",
    permission: { module: "ROLE MANAGEMENT" },
    icon: <FaShieldAlt />,
  },
  {
    label: "Manage Report",
    path: "/pms/customize_report",
    permission: { module: "CUSTOMIZE REPORT" },
    icon: <FaFileSignature />,
  },
  {
    label: "Manage Work Report",
    path: "/pms/manage_work_report",
    permission: { module: "WORK REPORT" },
    icon: <TbReportSearch />,
  },
  {
    label: "Add/Update Custom Fields",
    path: "/pms/addupdate_custom_fields",
    permission: { module: "CUSTOM FIELD MANAGEMENT" },
    icon: <FaPenFancy />,
  },
  {
    label: "Arrange Custom Fields",
    path: "/pms/arrange_custom_fields",
    permission: { module: "CUSTOM FIELD ARRANGEMENT" },
    icon: <FaGripHorizontal />,
  },
];
