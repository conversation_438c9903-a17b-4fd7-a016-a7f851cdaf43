"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/createTrackSheet.tsx":
/*!***************************************************!*\
  !*** ./app/user/trackSheets/createTrackSheet.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/_component/FormInput */ \"(app-pages-browser)/./app/_component/FormInput.tsx\");\n/* harmony import */ var _app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/_component/FormCheckboxGroup */ \"(app-pages-browser)/./app/_component/FormCheckboxGroup.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* harmony import */ var _app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/_component/PageInput */ \"(app-pages-browser)/./app/_component/PageInput.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./LegrandDetailsComponent */ \"(app-pages-browser)/./app/user/trackSheets/LegrandDetailsComponent.tsx\");\n/* harmony import */ var _ClientSelectPage__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./ClientSelectPage */ \"(app-pages-browser)/./app/user/trackSheets/ClientSelectPage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst validateFtpPageFormat = (value)=>{\n    if (!value || value.trim() === \"\") return false;\n    const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n    const match = value.match(ftpPageRegex);\n    if (!match) return false;\n    const currentPage = parseInt(match[1], 10);\n    const totalPages = parseInt(match[2], 10);\n    return currentPage > 0 && totalPages > 0 && currentPage <= totalPages;\n};\nconst trackSheetSchema = zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Client is required\"),\n    entries: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n        company: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Company is required\"),\n        division: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoice: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice is required\"),\n        masterInvoice: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        bol: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoiceDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice date is required\"),\n        receivedDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Received date is required\"),\n        shipmentDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        carrierName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Carrier name is required\"),\n        invoiceStatus: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice status is required\"),\n        manualMatching: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Manual matching is required\"),\n        invoiceType: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice type is required\"),\n        billToClient: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        currency: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Currency is required\"),\n        qtyShipped: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        weightUnitName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Weight unit is required\"),\n        quantityBilledText: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoiceTotal: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice total is required\"),\n        savings: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        financialNotes: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        ftpFileName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"FTP File Name is required\"),\n        ftpPage: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"FTP Page is required\").refine((value)=>validateFtpPageFormat(value), (value)=>{\n            if (!value || value.trim() === \"\") {\n                return {\n                    message: \"FTP Page is required\"\n                };\n            }\n            const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n            const match = value.match(ftpPageRegex);\n            if (!match) {\n                return {\n                    message: \"\"\n                };\n            }\n            const currentPage = parseInt(match[1], 10);\n            const totalPages = parseInt(match[2], 10);\n            if (currentPage <= 0 || totalPages <= 0) {\n                return {\n                    message: \"Page numbers must be positive (greater than 0)\"\n                };\n            }\n            if (currentPage > totalPages) {\n                return {\n                    message: \"Please enter a page number between \".concat(totalPages, \" and \").concat(currentPage, \" \")\n                };\n            }\n            return {\n                message: \"Invalid page format\"\n            };\n        }),\n        docAvailable: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.string()).optional().default([]),\n        otherDocuments: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        notes: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        //mistake: z.string().optional(),\n        legrandAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandCompanyName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        customFields: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_16__.z.string(),\n            name: zod__WEBPACK_IMPORTED_MODULE_16__.z.string(),\n            type: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n            value: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional()\n        })).default([])\n    }))\n});\nconst CreateTrackSheet = (param)=>{\n    let { client, carrier, associate, userData, activeView, setActiveView, permissions, carrierDataUpdate, clientDataUpdate } = param;\n    _s();\n    const companyFieldRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [generatedFilenames, setGeneratedFilenames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filenameValidation, setFilenameValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [missingFields, setMissingFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [legrandData, setLegrandData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [manualMatchingData, setManualMatchingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customFieldsRefresh, setCustomFieldsRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showFullForm, setShowFullForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [initialAssociateId, setInitialAssociateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialClientId, setInitialClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const selectionForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm)({\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\"\n        }\n    });\n    const associateOptions = associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n        var _a_id;\n        return {\n            value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n            label: a.name,\n            name: a.name\n        };\n    });\n    const carrierOptions = carrier === null || carrier === void 0 ? void 0 : carrier.map((c)=>{\n        var _c_id;\n        return {\n            value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n            label: c.name\n        };\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(trackSheetSchema),\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\",\n            entries: [\n                {\n                    company: \"\",\n                    division: \"\",\n                    invoice: \"\",\n                    masterInvoice: \"\",\n                    bol: \"\",\n                    invoiceDate: \"\",\n                    receivedDate: \"\",\n                    shipmentDate: \"\",\n                    carrierName: \"\",\n                    invoiceStatus: \"ENTRY\",\n                    manualMatching: \"\",\n                    invoiceType: \"\",\n                    billToClient: \"yes\",\n                    currency: \"\",\n                    qtyShipped: \"\",\n                    weightUnitName: \"\",\n                    quantityBilledText: \"\",\n                    invoiceTotal: \"\",\n                    savings: \"\",\n                    financialNotes: \"\",\n                    ftpFileName: \"\",\n                    ftpPage: \"\",\n                    docAvailable: [],\n                    otherDocuments: \"\",\n                    notes: \"\",\n                    //mistake: \"\",\n                    legrandAlias: \"\",\n                    legrandCompanyName: \"\",\n                    legrandAddress: \"\",\n                    legrandZipcode: \"\",\n                    shipperAlias: \"\",\n                    shipperAddress: \"\",\n                    shipperZipcode: \"\",\n                    consigneeAlias: \"\",\n                    consigneeAddress: \"\",\n                    consigneeZipcode: \"\",\n                    billtoAlias: \"\",\n                    billtoAddress: \"\",\n                    billtoZipcode: \"\",\n                    customFields: []\n                }\n            ]\n        }\n    });\n    const getFilteredClientOptions = ()=>{\n        if (!initialAssociateId) {\n            return (client === null || client === void 0 ? void 0 : client.map((c)=>{\n                var _c_id;\n                return {\n                    value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                    label: c.client_name,\n                    name: c.client_name\n                };\n            })) || [];\n        }\n        const filteredClients = (client === null || client === void 0 ? void 0 : client.filter((c)=>{\n            var _c_associateId;\n            return ((_c_associateId = c.associateId) === null || _c_associateId === void 0 ? void 0 : _c_associateId.toString()) === initialAssociateId;\n        })) || [];\n        return filteredClients.map((c)=>{\n            var _c_id;\n            return {\n                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                label: c.client_name,\n                name: c.client_name\n            };\n        });\n    };\n    const clientOptions = getFilteredClientOptions();\n    const entries = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useWatch)({\n        control: form.control,\n        name: \"entries\"\n    });\n    const validateClientForAssociate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, currentClientId)=>{\n        if (associateId && currentClientId) {\n            var _currentClient_associateId;\n            const currentClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === currentClientId;\n            });\n            if (currentClient && ((_currentClient_associateId = currentClient.associateId) === null || _currentClient_associateId === void 0 ? void 0 : _currentClient_associateId.toString()) !== associateId) {\n                form.setValue(\"clientId\", \"\");\n                setInitialClientId(\"\");\n                return false;\n            }\n        }\n        return true;\n    }, [\n        client,\n        form\n    ]);\n    const clearEntrySpecificClients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const currentEntries = form.getValues(\"entries\") || [];\n        if (currentEntries.length > 0) {\n            const hasEntrySpecificClients = currentEntries.some((entry)=>entry.clientId);\n            if (hasEntrySpecificClients) {\n                const updatedEntries = currentEntries.map((entry)=>({\n                        ...entry,\n                        clientId: \"\"\n                    }));\n                form.setValue(\"entries\", updatedEntries);\n            }\n        }\n    }, [\n        form\n    ]);\n    const fetchLegrandData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.legrandMapping_routes.GET_LEGRAND_MAPPINGS);\n            if (response && Array.isArray(response)) {\n                setLegrandData(response);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Error fetching LEGRAND mapping data:\", error);\n        }\n    }, []);\n    const fetchManualMatchingData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.manualMatchingMapping_routes.GET_MANUAL_MATCHING_MAPPINGS);\n            if (response && Array.isArray(response)) {\n                setManualMatchingData(response);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Error fetching manual matching mapping data:\", error);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchLegrandData();\n        fetchManualMatchingData();\n    }, [\n        fetchLegrandData,\n        fetchManualMatchingData\n    ]);\n    const handleLegrandDataChange = (entryIndex, businessUnit, divisionCode)=>{\n        form.setValue(\"entries.\".concat(entryIndex, \".company\"), businessUnit);\n        if (divisionCode) {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), divisionCode);\n            handleManualMatchingAutoFill(entryIndex, divisionCode);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), \"\");\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), \"\");\n        }\n    };\n    const handleManualMatchingAutoFill = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, division)=>{\n        var _formValues_entries, _clientOptions_find;\n        if (!division || !manualMatchingData.length) {\n            return;\n        }\n        const formValues = form.getValues();\n        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        if (entryClientName !== \"LEGRAND\") {\n            return;\n        }\n        const matchingEntry = manualMatchingData.find((mapping)=>mapping.division === division);\n        if (matchingEntry && matchingEntry.ManualShipment) {\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), matchingEntry.ManualShipment);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), \"\");\n        }\n    }, [\n        form,\n        manualMatchingData,\n        clientOptions\n    ]);\n    const fetchCustomFieldsForClient = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (clientId)=>{\n        if (!clientId) return [];\n        try {\n            const allCustomFieldsResponse = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS, \"/\").concat(clientId));\n            let customFieldsData = [];\n            if (allCustomFieldsResponse && allCustomFieldsResponse.custom_fields && allCustomFieldsResponse.custom_fields.length > 0) {\n                customFieldsData = allCustomFieldsResponse.custom_fields.map((field)=>{\n                    let autoFilledValue = \"\";\n                    if (field.type === \"AUTO\") {\n                        if (field.autoOption === \"DATE\") {\n                            autoFilledValue = new Date().toISOString().split(\"T\")[0];\n                        } else if (field.autoOption === \"USERNAME\") {\n                            autoFilledValue = (userData === null || userData === void 0 ? void 0 : userData.username) || \"\";\n                        }\n                    }\n                    return {\n                        id: field.id,\n                        name: field.name,\n                        type: field.type,\n                        autoOption: field.autoOption,\n                        value: autoFilledValue\n                    };\n                });\n            }\n            return customFieldsData;\n        } catch (error) {\n            return [];\n        }\n    }, [\n        userData\n    ]);\n    // const { fields, append, remove } = useFieldArray({\n    //   control: form.control,\n    //   name: \"entries\",\n    // });\n    // For now, we'll work with a single entry\n    const fields = [\n        {\n            id: \"single-entry\"\n        }\n    ];\n    // const append = () => {};\n    // const remove = () => {};\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        companyFieldRefs.current = companyFieldRefs.current.slice(0, fields.length);\n    }, [\n        fields.length\n    ]);\n    const generateFilename = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, formValues)=>{\n        try {\n            const entry = formValues.entries[entryIndex];\n            if (!entry) return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Entry data\"\n                ]\n            };\n            const missing = [];\n            const selectedAssociate = associate === null || associate === void 0 ? void 0 : associate.find((a)=>{\n                var _a_id;\n                return ((_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString()) === formValues.associateId;\n            });\n            const associateName = (selectedAssociate === null || selectedAssociate === void 0 ? void 0 : selectedAssociate.name) || \"\";\n            if (!associateName) {\n                missing.push(\"Associate\");\n            }\n            const entryClientId = entry.clientId || formValues.clientId;\n            const selectedClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entryClientId;\n            });\n            const clientName = (selectedClient === null || selectedClient === void 0 ? void 0 : selectedClient.client_name) || \"\";\n            if (!clientName) {\n                missing.push(\"Client\");\n            }\n            let carrierName = \"\";\n            if (entry.carrierName) {\n                const carrierOption = carrier === null || carrier === void 0 ? void 0 : carrier.find((c)=>{\n                    var _c_id;\n                    return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entry.carrierName;\n                });\n                carrierName = (carrierOption === null || carrierOption === void 0 ? void 0 : carrierOption.name) || \"\";\n            }\n            if (!carrierName) {\n                missing.push(\"Carrier\");\n            }\n            const receivedDate = entry.receivedDate;\n            const invoiceDate = entry.invoiceDate;\n            const currentDate = new Date();\n            const year = currentDate.getFullYear().toString();\n            const month = currentDate.toLocaleString(\"default\", {\n                month: \"short\"\n            }).toUpperCase();\n            if (!invoiceDate) {\n                missing.push(\"Invoice Date\");\n            }\n            let receivedDateStr = \"\";\n            if (receivedDate) {\n                const dateParts = receivedDate.split(\"/\");\n                if (dateParts.length === 3) {\n                    const day = parseInt(dateParts[0], 10);\n                    const month = parseInt(dateParts[1], 10);\n                    const year = parseInt(dateParts[2], 10);\n                    const paddedMonth = month.toString().padStart(2, \"0\");\n                    const paddedDay = day.toString().padStart(2, \"0\");\n                    receivedDateStr = \"\".concat(year, \"-\").concat(paddedMonth, \"-\").concat(paddedDay);\n                } else {\n                    const date = new Date(receivedDate);\n                    receivedDateStr = date.toISOString().split(\"T\")[0];\n                }\n            } else {\n                missing.push(\"Received Date\");\n            }\n            const ftpFileName = entry.ftpFileName || \"\";\n            const baseFilename = ftpFileName ? ftpFileName.endsWith(\".pdf\") ? ftpFileName : \"\".concat(ftpFileName, \".pdf\") : \"\";\n            if (!baseFilename) {\n                missing.push(\"FTP File Name\");\n            }\n            const isValid = missing.length === 0;\n            const filename = isValid ? \"/\".concat(associateName, \"/\").concat(clientName, \"/CARRIERINVOICES/\").concat(carrierName, \"/\").concat(year, \"/\").concat(month, \"/\").concat(receivedDateStr, \"/\").concat(baseFilename) : \"\";\n            return {\n                filename,\n                isValid,\n                missing\n            };\n        } catch (error) {\n            return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Error generating filename\"\n                ]\n            };\n        }\n    }, [\n        client,\n        carrier,\n        associate\n    ]);\n    const handleCompanyAutoPopulation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, entryClientId)=>{\n        var _clientOptions_find;\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        const currentEntry = form.getValues(\"entries.\".concat(entryIndex));\n        if (entryClientName && entryClientName !== \"LEGRAND\") {\n            form.setValue(\"entries.\".concat(entryIndex, \".company\"), entryClientName);\n        } else if (entryClientName === \"LEGRAND\") {\n            const shipperAlias = currentEntry.shipperAlias;\n            const consigneeAlias = currentEntry.consigneeAlias;\n            const billtoAlias = currentEntry.billtoAlias;\n            const hasAnyLegrandData = shipperAlias || consigneeAlias || billtoAlias;\n            if (!hasAnyLegrandData && currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        } else {\n            if (currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        }\n    }, [\n        form,\n        clientOptions\n    ]);\n    const handleCustomFieldsFetch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (entryIndex, entryClientId)=>{\n        var _currentCustomFields_, _currentCustomFields_1;\n        if (!entryClientId) {\n            const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\"));\n            if (currentCustomFields && currentCustomFields.length > 0) {\n                form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), []);\n            }\n            return;\n        }\n        const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\")) || [];\n        const hasEmptyAutoUsernameFields = currentCustomFields.some((field)=>field.type === \"AUTO\" && field.autoOption === \"USERNAME\" && !field.value && (userData === null || userData === void 0 ? void 0 : userData.username));\n        const shouldFetchCustomFields = currentCustomFields.length === 0 || currentCustomFields.length > 0 && !((_currentCustomFields_ = currentCustomFields[0]) === null || _currentCustomFields_ === void 0 ? void 0 : _currentCustomFields_.clientId) || ((_currentCustomFields_1 = currentCustomFields[0]) === null || _currentCustomFields_1 === void 0 ? void 0 : _currentCustomFields_1.clientId) !== entryClientId || hasEmptyAutoUsernameFields;\n        if (shouldFetchCustomFields) {\n            const customFieldsData = await fetchCustomFieldsForClient(entryClientId);\n            const fieldsWithClientId = customFieldsData.map((field)=>({\n                    ...field,\n                    clientId: entryClientId\n                }));\n            form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), fieldsWithClientId);\n            setTimeout(()=>{\n                fieldsWithClientId.forEach((field, fieldIndex)=>{\n                    const fieldPath = \"entries.\".concat(entryIndex, \".customFields.\").concat(fieldIndex, \".value\");\n                    if (field.value) {\n                        form.setValue(fieldPath, field.value);\n                    }\n                });\n                setCustomFieldsRefresh((prev)=>prev + 1);\n            }, 100);\n        }\n    }, [\n        form,\n        fetchCustomFieldsForClient,\n        userData === null || userData === void 0 ? void 0 : userData.username\n    ]);\n    const updateFilenames = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const formValues = form.getValues();\n        const newFilenames = [];\n        const newValidation = [];\n        const newMissingFields = [];\n        if (formValues.entries && Array.isArray(formValues.entries)) {\n            formValues.entries.forEach((_, index)=>{\n                const { filename, isValid, missing } = generateFilename(index, formValues);\n                newFilenames[index] = filename;\n                newValidation[index] = isValid;\n                newMissingFields[index] = missing || [];\n            });\n        }\n        setGeneratedFilenames(newFilenames);\n        setFilenameValidation(newValidation);\n        setMissingFields(newMissingFields);\n    }, [\n        form,\n        generateFilename\n    ]);\n    const handleInitialSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, clientId)=>{\n        form.setValue(\"associateId\", associateId);\n        form.setValue(\"clientId\", clientId);\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(0, clientId);\n            handleCustomFieldsFetch(0, clientId);\n            updateFilenames();\n        }, 50);\n        setShowFullForm(true);\n    }, [\n        form,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch,\n        updateFilenames\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timeoutId = setTimeout(()=>{\n            updateFilenames();\n            const formValues = form.getValues();\n            if (formValues.entries && Array.isArray(formValues.entries)) {\n                formValues.entries.forEach((entry, index)=>{\n                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (index === 0 ? formValues.clientId : \"\");\n                    if (entryClientId) {\n                        handleCustomFieldsFetch(index, entryClientId);\n                    }\n                });\n            }\n        }, 50);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        updateFilenames,\n        handleCustomFieldsFetch,\n        form\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const subscription = form.watch((_, param)=>{\n            let { name } = param;\n            if (name && (name.includes(\"associateId\") || name.includes(\"clientId\") || name.includes(\"carrierName\") || name.includes(\"invoiceDate\") || name.includes(\"receivedDate\") || name.includes(\"ftpFileName\") || name.includes(\"company\") || name.includes(\"division\"))) {\n                updateFilenames();\n                if (name.includes(\"division\")) {\n                    const entryMatch = name.match(/entries\\.(\\d+)\\.division/);\n                    if (entryMatch) {\n                        var _formValues_entries, _clientOptions_find;\n                        const entryIndex = parseInt(entryMatch[1], 10);\n                        const formValues = form.getValues();\n                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n                        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n                        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                        if (entryClientName === \"LEGRAND\") {\n                            var _formValues_entries_entryIndex, _formValues_entries1;\n                            const divisionValue = (_formValues_entries1 = formValues.entries) === null || _formValues_entries1 === void 0 ? void 0 : (_formValues_entries_entryIndex = _formValues_entries1[entryIndex]) === null || _formValues_entries_entryIndex === void 0 ? void 0 : _formValues_entries_entryIndex.division;\n                            if (divisionValue) {\n                                setTimeout(()=>{\n                                    handleManualMatchingAutoFill(entryIndex, divisionValue);\n                                }, 50);\n                            }\n                        }\n                    }\n                }\n                if (name.includes(\"clientId\")) {\n                    const entryMatch = name.match(/entries\\.(\\d+)\\.clientId/);\n                    if (entryMatch) {\n                        const entryIndex = parseInt(entryMatch[1], 10);\n                        setTimeout(()=>{\n                            var _formValues_entries, _clientOptions_find;\n                            const formValues = form.getValues();\n                            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n                            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                            if (entryClientName === \"LEGRAND\" && (entry === null || entry === void 0 ? void 0 : entry.division)) {\n                                handleManualMatchingAutoFill(entryIndex, entry.division);\n                            }\n                        }, 100);\n                    }\n                }\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        form,\n        updateFilenames,\n        handleManualMatchingAutoFill,\n        clientOptions\n    ]);\n    const onSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (values)=>{\n        try {\n            const currentFormValues = form.getValues();\n            const currentValidation = [];\n            const currentMissingFields = [];\n            const currentFilenames = [];\n            if (currentFormValues.entries && Array.isArray(currentFormValues.entries)) {\n                currentFormValues.entries.forEach((_, index)=>{\n                    const { filename, isValid, missing } = generateFilename(index, currentFormValues);\n                    currentValidation[index] = isValid;\n                    currentMissingFields[index] = missing || [];\n                    currentFilenames[index] = filename;\n                });\n            }\n            const allFilenamesValid = currentValidation.every((isValid)=>isValid);\n            if (!allFilenamesValid) {\n                const invalidEntries = currentValidation.map((isValid, index)=>({\n                        index,\n                        isValid,\n                        missing: currentMissingFields[index]\n                    })).filter((entry)=>!entry.isValid);\n                const errorDetails = invalidEntries.map((entry)=>\"Entry \".concat(entry.index + 1, \": \").concat(entry.missing.join(\", \"))).join(\" | \");\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Cannot submit: Missing fields - \".concat(errorDetails));\n                return;\n            }\n            const entries = values.entries.map((entry, index)=>{\n                var _entry_customFields;\n                return {\n                    company: entry.company,\n                    division: entry.division,\n                    invoice: entry.invoice,\n                    masterInvoice: entry.masterInvoice,\n                    bol: entry.bol,\n                    invoiceDate: entry.invoiceDate,\n                    receivedDate: entry.receivedDate,\n                    shipmentDate: entry.shipmentDate,\n                    carrierId: entry.carrierName,\n                    invoiceStatus: entry.invoiceStatus,\n                    manualMatching: entry.manualMatching,\n                    invoiceType: entry.invoiceType,\n                    billToClient: entry.billToClient,\n                    currency: entry.currency,\n                    qtyShipped: entry.qtyShipped,\n                    weightUnitName: entry.weightUnitName,\n                    quantityBilledText: entry.quantityBilledText,\n                    invoiceTotal: entry.invoiceTotal,\n                    savings: entry.savings,\n                    ftpFileName: entry.ftpFileName,\n                    ftpPage: entry.ftpPage,\n                    docAvailable: entry.docAvailable,\n                    notes: entry.notes,\n                    //mistake: entry.mistake,\n                    filePath: generatedFilenames[index],\n                    customFields: (_entry_customFields = entry.customFields) === null || _entry_customFields === void 0 ? void 0 : _entry_customFields.map((cf)=>({\n                            id: cf.id,\n                            value: cf.value\n                        }))\n                };\n            });\n            const formData = {\n                clientId: values.clientId,\n                entries: entries\n            };\n            const result = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.formSubmit)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.trackSheets_routes.CREATE_TRACK_SHEETS, \"POST\", formData);\n            if (result.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"All TrackSheets created successfully\");\n                form.reset();\n                setTimeout(()=>{\n                    handleInitialSelection(initialAssociateId, initialClientId);\n                }, 100);\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(result.message || \"Failed to create TrackSheets\");\n            }\n            router.refresh();\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"An error occurred while creating the TrackSheets\");\n        }\n    }, [\n        form,\n        router,\n        generateFilename,\n        initialAssociateId,\n        initialClientId,\n        handleInitialSelection,\n        generatedFilenames\n    ]);\n    // const addNewEntry = useCallback(() => {\n    //   const newIndex = fields.length;\n    //   append({\n    //     clientId: initialClientId,\n    //     company: \"\",\n    //     division: \"\",\n    //     invoice: \"\",\n    //     masterInvoice: \"\",\n    //     bol: \"\",\n    //     invoiceDate: \"\",\n    //     receivedDate: \"\",\n    //     shipmentDate: \"\",\n    //     carrierName: \"\",\n    //     invoiceStatus: \"ENTRY\",\n    //     manualMatching: \"\",\n    //     invoiceType: \"\",\n    //     billToClient: \"\",\n    //     currency: \"\",\n    //     qtyShipped: \"\",\n    //     weightUnitName: \"\",\n    //     quantityBilledText: \"\",\n    //     invoiceTotal: \"\",\n    //     savings: \"\",\n    //     financialNotes: \"\",\n    //     ftpFileName: \"\",\n    //     ftpPage: \"\",\n    //     docAvailable: [],\n    //     otherDocuments: \"\",\n    //     notes: \"\",\n    //     //mistake: \"\",\n    //     legrandAlias: \"\",\n    //     legrandCompanyName: \"\",\n    //     legrandAddress: \"\",\n    //     legrandZipcode: \"\",\n    //     shipperAlias: \"\",\n    //     shipperAddress: \"\",\n    //     shipperZipcode: \"\",\n    //     consigneeAlias: \"\",\n    //     consigneeAddress: \"\",\n    //     consigneeZipcode: \"\",\n    //     billtoAlias: \"\",\n    //     billtoAddress: \"\",\n    //     billtoZipcode: \"\",\n    //     customFields: [],\n    //   } as any);\n    //   setTimeout(() => {\n    //     handleCompanyAutoPopulation(newIndex, initialClientId);\n    //     handleCustomFieldsFetch(newIndex, initialClientId);\n    //     if (companyFieldRefs.current[newIndex]) {\n    //       const inputElement =\n    //         companyFieldRefs.current[newIndex]?.querySelector(\"input\") ||\n    //         companyFieldRefs.current[newIndex]?.querySelector(\"button\") ||\n    //         companyFieldRefs.current[newIndex]?.querySelector(\"select\");\n    //       if (inputElement) {\n    //         inputElement.focus();\n    //         try {\n    //           inputElement.click();\n    //         } catch (e) {}\n    //       }\n    //     }\n    //     updateFilenames();\n    //   }, 200);\n    // }, [\n    //   append,\n    //   fields.length,\n    //   updateFilenames,\n    //   initialClientId,\n    //   handleCompanyAutoPopulation,\n    //   handleCustomFieldsFetch,\n    // ]);\n    const handleFormKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.ctrlKey && (e.key === \"s\" || e.key === \"S\")) {\n            e.preventDefault();\n            form.handleSubmit(onSubmit)();\n        } else if (e.key === \"Enter\" && !e.ctrlKey && !e.shiftKey && !e.altKey) {\n            const activeElement = document.activeElement;\n            const isSubmitButton = (activeElement === null || activeElement === void 0 ? void 0 : activeElement.getAttribute(\"type\")) === \"submit\";\n            if (isSubmitButton) {\n                e.preventDefault();\n                form.handleSubmit(onSubmit)();\n            }\n        }\n    }, [\n        form,\n        onSubmit\n    ]);\n    // const removeEntry = (index: number) => {\n    //   if (fields.length > 1) {\n    //     remove(index);\n    //   } else {\n    //     toast.error(\"You must have at least one entry\");\n    //   }\n    // };\n    const getFilteredDivisionOptions = (company, entryIndex)=>{\n        if (!company || !legrandData.length) {\n            return [];\n        }\n        if (entryIndex !== undefined) {\n            var _formValues_entries, _clientOptions_find;\n            const formValues = form.getValues();\n            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n            if (entryClientName === \"LEGRAND\") {\n                const shipperAlias = form.getValues(\"entries.\".concat(entryIndex, \".shipperAlias\"));\n                const consigneeAlias = form.getValues(\"entries.\".concat(entryIndex, \".consigneeAlias\"));\n                const billtoAlias = form.getValues(\"entries.\".concat(entryIndex, \".billtoAlias\"));\n                const currentAlias = shipperAlias || consigneeAlias || billtoAlias;\n                if (currentAlias) {\n                    const selectedData = legrandData.find((data)=>{\n                        const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                        return uniqueKey === currentAlias;\n                    });\n                    if (selectedData) {\n                        const baseAliasName = selectedData.aliasShippingNames && selectedData.aliasShippingNames !== \"NONE\" ? selectedData.aliasShippingNames : selectedData.legalName;\n                        const sameAliasEntries = legrandData.filter((data)=>{\n                            const dataAliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : data.legalName;\n                            return dataAliasName === baseAliasName;\n                        });\n                        const allDivisions = [];\n                        sameAliasEntries.forEach((entry)=>{\n                            if (entry.customeCode) {\n                                if (entry.customeCode.includes(\"/\")) {\n                                    const splitDivisions = entry.customeCode.split(\"/\").map((d)=>d.trim());\n                                    allDivisions.push(...splitDivisions);\n                                } else {\n                                    allDivisions.push(entry.customeCode);\n                                }\n                            }\n                        });\n                        const uniqueDivisions = Array.from(new Set(allDivisions.filter((code)=>code)));\n                        if (uniqueDivisions.length > 1) {\n                            const contextDivisions = uniqueDivisions.sort().map((code)=>({\n                                    value: code,\n                                    label: code\n                                }));\n                            return contextDivisions;\n                        } else {}\n                    }\n                }\n            }\n        }\n        const allDivisions = [];\n        legrandData.filter((data)=>data.businessUnit === company && data.customeCode).forEach((data)=>{\n            if (data.customeCode.includes(\"/\")) {\n                const splitDivisions = data.customeCode.split(\"/\").map((d)=>d.trim());\n                allDivisions.push(...splitDivisions);\n            } else {\n                allDivisions.push(data.customeCode);\n            }\n        });\n        const divisions = Array.from(new Set(allDivisions.filter((code)=>code))).sort().map((code)=>({\n                value: code,\n                label: code\n            }));\n        return divisions;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-2 py-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4 pl-3 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                variant: \"default\",\n                                onClick: ()=>setActiveView(\"view\"),\n                                className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"view\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                children: \"View TrackSheet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1124,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                variant: \"default\",\n                                onClick: ()=>setActiveView(\"create\"),\n                                className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"create\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                children: \"Create TrackSheet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1135,\n                                columnNumber: 13\n                            }, undefined),\n                            activeView === \"create\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-2 ml-4 flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-4 h-4 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1151,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-sm font-semibold text-gray-900\",\n                                                children: \"Create TrackSheet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1152,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1150,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n                                        ...selectionForm,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        form: selectionForm,\n                                                        name: \"associateId\",\n                                                        label: \"Select Associate\",\n                                                        placeholder: \"Search Associate...\",\n                                                        isRequired: true,\n                                                        options: associateOptions || [],\n                                                        onValueChange: (value)=>{\n                                                            setInitialAssociateId(value);\n                                                            if (value && initialClientId) {\n                                                                validateClientForAssociate(value, initialClientId);\n                                                            } else {\n                                                                setInitialClientId(\"\");\n                                                                selectionForm.setValue(\"clientId\", \"\");\n                                                            }\n                                                            setShowFullForm(false);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1160,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1159,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        form: selectionForm,\n                                                        name: \"clientId\",\n                                                        label: \"Select Client\",\n                                                        placeholder: \"Search Client...\",\n                                                        isRequired: true,\n                                                        disabled: !initialAssociateId,\n                                                        options: clientOptions || [],\n                                                        onValueChange: (value)=>{\n                                                            setInitialClientId(value);\n                                                            if (showFullForm) {\n                                                                form.reset();\n                                                                clearEntrySpecificClients();\n                                                            }\n                                                            if (value && initialAssociateId) {\n                                                                setTimeout(()=>{\n                                                                    handleInitialSelection(initialAssociateId, value);\n                                                                }, 100);\n                                                            } else {\n                                                                setShowFullForm(false);\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1182,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1181,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                            lineNumber: 1158,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1157,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1149,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1123,\n                        columnNumber: 11\n                    }, undefined),\n                    activeView === \"view\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientSelectPage__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            permissions: permissions,\n                            client: client,\n                            clientDataUpdate: clientDataUpdate,\n                            carrierDataUpdate: carrierDataUpdate\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                            lineNumber: 1217,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1216,\n                        columnNumber: 13\n                    }, undefined) : /* Form Section - Only show when both associate and client are selected */ showFullForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n                        ...form,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: form.handleSubmit(onSubmit),\n                            onKeyDown: handleFormKeyDown,\n                            className: \"space-y-3\",\n                            children: [\n                                fields.map((field, index)=>{\n                                    var _missingFields_index;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2 bg-gray-100 rounded-md px-3 py-2 border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold text-xs\",\n                                                            children: index + 1\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1233,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                            children: [\n                                                                \"Entry #\",\n                                                                index + 1\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1236,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1232,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1231,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 rounded-md p-2 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1249,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: \"Client Information\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1250,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1248,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"mt-2\",\n                                                                                form: form,\n                                                                                label: \"FTP File Name\",\n                                                                                name: \"entries.\".concat(index, \".ftpFileName\"),\n                                                                                type: \"text\",\n                                                                                isRequired: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1257,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1256,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"mt-2\",\n                                                                            form: form,\n                                                                            label: \"FTP Page\",\n                                                                            name: \"entries.\".concat(index, \".ftpPage\"),\n                                                                            isRequired: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1266,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"mt-0\",\n                                                                                form: form,\n                                                                                name: \"entries.\".concat(index, \".carrierName\"),\n                                                                                label: \"Select Carrier\",\n                                                                                placeholder: \"Search Carrier\",\n                                                                                isRequired: true,\n                                                                                options: (carrierOptions === null || carrierOptions === void 0 ? void 0 : carrierOptions.filter((carrier)=>{\n                                                                                    const currentEntries = form.getValues(\"entries\") || [];\n                                                                                    const isSelectedInOtherEntries = currentEntries.some((entry, entryIndex)=>entryIndex !== index && entry.carrierName === carrier.value);\n                                                                                    return !isSelectedInOtherEntries;\n                                                                                })) || [],\n                                                                                onValueChange: ()=>{\n                                                                                    setTimeout(()=>updateFilenames(), 100);\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1274,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1273,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: (()=>{\n                                                                                var _formValues_entries, _clientOptions_find;\n                                                                                const formValues = form.getValues();\n                                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-sm font-medium text-gray-700 mb-1 block\",\n                                                                                            children: [\n                                                                                                \"Billed to \",\n                                                                                                entryClientName || \"Client\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1316,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-4\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                    className: \"flex items-center\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                            type: \"radio\",\n                                                                                                            ...form.register(\"entries.\".concat(index, \".billToClient\")),\n                                                                                                            value: \"yes\",\n                                                                                                            defaultChecked: true,\n                                                                                                            className: \"mr-2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1321,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            className: \"text-sm\",\n                                                                                                            children: \"Yes\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1328,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1320,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                    className: \"flex items-center\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                            type: \"radio\",\n                                                                                                            ...form.register(\"entries.\".concat(index, \".billToClient\")),\n                                                                                                            value: \"no\",\n                                                                                                            className: \"mr-2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1331,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            className: \"text-sm\",\n                                                                                                            children: \"No\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1337,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1330,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1319,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1315,\n                                                                                    columnNumber: 37\n                                                                                }, undefined);\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1299,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1255,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                (()=>{\n                                                                    var _formValues_entries, _clientOptions_find;\n                                                                    const formValues = form.getValues();\n                                                                    const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                    const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                    return entryClientName === \"LEGRAND\";\n                                                                })() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-3 mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Shipper\",\n                                                                                fieldPrefix: \"shipper\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1360,\n                                                                                columnNumber: 35\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Consignee\",\n                                                                                fieldPrefix: \"consignee\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1369,\n                                                                                columnNumber: 35\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Bill-to\",\n                                                                                fieldPrefix: \"billto\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1378,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1359,\n                                                                        columnNumber: 33\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1358,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            ref: (el)=>{\n                                                                                companyFieldRefs.current[index] = el;\n                                                                            },\n                                                                            className: \"flex flex-col mb-1 [&_input]:h-10\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                form: form,\n                                                                                label: \"Company\",\n                                                                                name: \"entries.\".concat(index, \".company\"),\n                                                                                type: \"text\",\n                                                                                disable: (()=>{\n                                                                                    var _formValues_entries, _clientOptions_find;\n                                                                                    const formValues = form.getValues();\n                                                                                    const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                    const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                    return entryClientName === \"LEGRAND\";\n                                                                                })()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1399,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1393,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: (()=>{\n                                                                                var _formValues_entries, _clientOptions_find, _entries_index;\n                                                                                const formValues = form.getValues();\n                                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                const isLegrand = entryClientName === \"LEGRAND\";\n                                                                                return isLegrand ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    form: form,\n                                                                                    name: \"entries.\".concat(index, \".division\"),\n                                                                                    label: \"Division\",\n                                                                                    placeholder: \"Search Division\",\n                                                                                    disabled: false,\n                                                                                    options: getFilteredDivisionOptions((entries === null || entries === void 0 ? void 0 : (_entries_index = entries[index]) === null || _entries_index === void 0 ? void 0 : _entries_index.company) || \"\", index),\n                                                                                    onValueChange: (value)=>{\n                                                                                        setTimeout(()=>{\n                                                                                            handleManualMatchingAutoFill(index, value);\n                                                                                        }, 10);\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1439,\n                                                                                    columnNumber: 37\n                                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                    form: form,\n                                                                                    label: \"Division\",\n                                                                                    name: \"entries.\".concat(index, \".division\"),\n                                                                                    type: \"text\",\n                                                                                    placeholder: \"Enter Division\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1459,\n                                                                                    columnNumber: 37\n                                                                                }, undefined);\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1421,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1392,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1247,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1245,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1476,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Document Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1477,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1475,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Master Invoice\",\n                                                                        name: \"entries.\".concat(index, \".masterInvoice\"),\n                                                                        type: \"text\",\n                                                                        onBlur: (e)=>{\n                                                                            const masterInvoiceValue = e.target.value;\n                                                                            if (masterInvoiceValue) {\n                                                                                form.setValue(\"entries.\".concat(index, \".invoice\"), masterInvoiceValue);\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1482,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice\",\n                                                                        name: \"entries.\".concat(index, \".invoice\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1497,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"BOL\",\n                                                                        name: \"entries.\".concat(index, \".bol\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1504,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1481,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Received Date\",\n                                                                        name: \"entries.\".concat(index, \".receivedDate\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true,\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1512,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Date\",\n                                                                        name: \"entries.\".concat(index, \".invoiceDate\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true,\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1520,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Shipment Date\",\n                                                                        name: \"entries.\".concat(index, \".shipmentDate\"),\n                                                                        type: \"text\",\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1528,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1511,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1474,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4 pb-4 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1541,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Financial & Shipment\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1542,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1540,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Total\",\n                                                                        name: \"entries.\".concat(index, \".invoiceTotal\"),\n                                                                        type: \"number\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1547,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        form: form,\n                                                                        name: \"entries.\".concat(index, \".currency\"),\n                                                                        label: \"Currency\",\n                                                                        placeholder: \"Search currency\",\n                                                                        isRequired: true,\n                                                                        options: [\n                                                                            {\n                                                                                value: \"USD\",\n                                                                                label: \"USD\"\n                                                                            },\n                                                                            {\n                                                                                value: \"CAD\",\n                                                                                label: \"CAD\"\n                                                                            },\n                                                                            {\n                                                                                value: \"EUR\",\n                                                                                label: \"EUR\"\n                                                                            }\n                                                                        ]\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1554,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Savings\",\n                                                                        name: \"entries.\".concat(index, \".savings\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1566,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Notes\",\n                                                                        name: \"entries.\".concat(index, \".financialNotes\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1572,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1546,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Quantity Shipped\",\n                                                                        name: \"entries.\".concat(index, \".qtyShipped\"),\n                                                                        type: \"number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1580,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Weight Unit\",\n                                                                        name: \"entries.\".concat(index, \".weightUnitName\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1586,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        form: form,\n                                                                        name: \"entries.\".concat(index, \".invoiceType\"),\n                                                                        label: \"Invoice Type\",\n                                                                        placeholder: \"Search Invoice Type\",\n                                                                        isRequired: true,\n                                                                        options: [\n                                                                            {\n                                                                                value: \"FREIGHT\",\n                                                                                label: \"FREIGHT\"\n                                                                            },\n                                                                            {\n                                                                                value: \"ADDITIONAL\",\n                                                                                label: \"ADDITIONAL\"\n                                                                            },\n                                                                            {\n                                                                                value: \"BALANCED DUE\",\n                                                                                label: \"BALANCED DUE\"\n                                                                            },\n                                                                            {\n                                                                                value: \"CREDIT\",\n                                                                                label: \"CREDIT\"\n                                                                            },\n                                                                            {\n                                                                                value: \"REVISED\",\n                                                                                label: \"REVISED\"\n                                                                            }\n                                                                        ]\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1593,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Quantity Billed Text\",\n                                                                        name: \"entries.\".concat(index, \".quantityBilledText\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1610,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1579,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Status\",\n                                                                        name: \"entries.\".concat(index, \".invoiceStatus\"),\n                                                                        type: \"text\",\n                                                                        disable: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1618,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1625,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1626,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1627,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1617,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1539,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1634,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Additional Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1635,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1633,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                                children: [\n                                                                    (()=>{\n                                                                        var _formValues_entries, _clientOptions_find;\n                                                                        const formValues = form.getValues();\n                                                                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                        const isLegrand = entryClientName === \"LEGRAND\";\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: isLegrand ? \"Manual or Matching\" : \"Manual or Matching\",\n                                                                            name: \"entries.\".concat(index, \".manualMatching\"),\n                                                                            type: \"text\",\n                                                                            isRequired: true,\n                                                                            disable: isLegrand,\n                                                                            placeholder: isLegrand ? \"Auto-filled based on division\" : \"Enter manual or matching\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1652,\n                                                                            columnNumber: 33\n                                                                        }, undefined);\n                                                                    })(),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Notes (Remarks)\",\n                                                                        name: \"entries.\".concat(index, \".notes\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1671,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            form: form,\n                                                                            label: \"Documents Available\",\n                                                                            name: \"entries.\".concat(index, \".docAvailable\"),\n                                                                            options: [\n                                                                                {\n                                                                                    label: \"Invoice\",\n                                                                                    value: \"Invoice\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"BOL\",\n                                                                                    value: \"Bol\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"POD\",\n                                                                                    value: \"Pod\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"Packages List\",\n                                                                                    value: \"Packages List\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"Other Documents\",\n                                                                                    value: \"Other Documents\"\n                                                                                }\n                                                                            ],\n                                                                            className: \"flex-row gap-2 text-xs\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1678,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1677,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    (()=>{\n                                                                        var _formValues_entries;\n                                                                        const formValues = form.getValues();\n                                                                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                        const docAvailable = (entry === null || entry === void 0 ? void 0 : entry.docAvailable) || [];\n                                                                        const hasOtherDocuments = docAvailable.includes(\"Other Documents\");\n                                                                        return hasOtherDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: \"Specify Other Documents\",\n                                                                            name: \"entries.\".concat(index, \".otherDocuments\"),\n                                                                            type: \"text\",\n                                                                            isRequired: true,\n                                                                            placeholder: \"Enter other document types...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1707,\n                                                                            columnNumber: 33\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1716,\n                                                                            columnNumber: 33\n                                                                        }, undefined);\n                                                                    })()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1639,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1632,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    (()=>{\n                                                        var _formValues_entries;\n                                                        const formValues = form.getValues();\n                                                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                        const customFields = (entry === null || entry === void 0 ? void 0 : entry.customFields) || [];\n                                                        return Array.isArray(customFields) && customFields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"pt-3 border-t border-gray-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-purple-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1742,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: [\n                                                                                \"Custom Fields (\",\n                                                                                customFields.length,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1743,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1741,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                                    children: customFields.map((cf, cfIdx)=>{\n                                                                        const fieldType = cf.type || \"TEXT\";\n                                                                        const isAutoField = fieldType === \"AUTO\";\n                                                                        const autoOption = cf.autoOption;\n                                                                        let inputType = \"text\";\n                                                                        if (fieldType === \"DATE\" || isAutoField && autoOption === \"DATE\") {\n                                                                            inputType = \"date\";\n                                                                        } else if (fieldType === \"NUMBER\") {\n                                                                            inputType = \"number\";\n                                                                        }\n                                                                        const fieldLabel = isAutoField ? \"\".concat(cf.name, \" (Auto - \").concat(autoOption, \")\") : cf.name;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: fieldLabel,\n                                                                            name: \"entries.\".concat(index, \".customFields.\").concat(cfIdx, \".value\"),\n                                                                            type: inputType,\n                                                                            className: \"w-full\",\n                                                                            disable: isAutoField\n                                                                        }, cf.id, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1768,\n                                                                            columnNumber: 37\n                                                                        }, undefined);\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1747,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, \"custom-fields-\".concat(index, \"-\").concat(customFieldsRefresh), true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1737,\n                                                            columnNumber: 29\n                                                        }, undefined) : null;\n                                                    })(),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-3 border-t border-gray-100 mt-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-end space-x-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                        asChild: true,\n                                                                        tabIndex: -1,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 rounded-full flex items-center justify-center text-white font-bold text-xs cursor-help transition-colors duration-200 \".concat(filenameValidation[index] ? \"bg-green-500 hover:bg-green-600\" : \"bg-orange-500 hover:bg-orange-600\"),\n                                                                            tabIndex: -1,\n                                                                            role: \"button\",\n                                                                            \"aria-label\": \"Entry \".concat(index + 1, \" filename status\"),\n                                                                            children: \"!\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1790,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1789,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                        side: \"top\",\n                                                                        align: \"center\",\n                                                                        className: \"z-[9999]\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm max-w-md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium mb-1\",\n                                                                                    children: [\n                                                                                        \"Entry #\",\n                                                                                        index + 1,\n                                                                                        \" Filename\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1811,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                filenameValidation[index] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium text-green-600 mb-2\",\n                                                                                            children: \"Filename Generated\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1816,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs font-mono break-all bg-gray-100 p-2 rounded text-black\",\n                                                                                            children: generatedFilenames[index]\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1819,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1815,\n                                                                                    columnNumber: 37\n                                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium text-orange-600 mb-1\",\n                                                                                            children: \"Please fill the form to generate filename\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1825,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-600 mb-2\",\n                                                                                            children: \"Missing fields:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1829,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                            className: \"list-disc list-inside space-y-1\",\n                                                                                            children: (_missingFields_index = missingFields[index]) === null || _missingFields_index === void 0 ? void 0 : _missingFields_index.map((field, fieldIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                    className: \"text-xs\",\n                                                                                                    children: field\n                                                                                                }, fieldIndex, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1835,\n                                                                                                    columnNumber: 45\n                                                                                                }, undefined))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1832,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1824,\n                                                                                    columnNumber: 37\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1810,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1805,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1788,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1786,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1785,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1243,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, field.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1229,\n                                        columnNumber: 21\n                                    }, undefined);\n                                }),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            type: \"submit\",\n                                            className: \"px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg text-sm\",\n                                            children: \"Save\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                            lineNumber: 1860,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1859,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                    lineNumber: 1858,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                            lineNumber: 1223,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1222,\n                        columnNumber: 15\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                lineNumber: 1121,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n            lineNumber: 1120,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n        lineNumber: 1119,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreateTrackSheet, \"JO7kwYkLnkYHPQYrleMXiv9SYDw=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useWatch\n    ];\n});\n_c = CreateTrackSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreateTrackSheet);\nvar _c;\n$RefreshReg$(_c, \"CreateTrackSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/createTrackSheet.tsx\n"));

/***/ })

});